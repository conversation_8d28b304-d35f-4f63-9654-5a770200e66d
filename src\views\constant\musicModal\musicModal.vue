<script setup>
import {inject, onMounted, nextTick,onUnmounted, onActivated,reactive, ref, watch, getCurrentInstance,defineExpose} from 'vue'
import audioPlay from './components/audioPlay.vue'
import AudioPlayer from "@/views/modules/AIDubbing/components/audioPlayer.vue";
import { getBGMIApi,get_signature,upload_callback } from '@/api_my/AlDubb'
import {useAIDubbingStore} from '@/stores/modules/AIDubbing.js'
import {useCommerDubbingStore} from '@/stores/modules/commercialDubbing.js'
import axios from "axios";
import { ElMessage } from 'element-plus'
import { useloginStore } from '@/stores/login'
let loginStore = useloginStore()
let userId=ref('')
let rate=ref(window.innerWidth/1920)
//
const useAIDubbing = useAIDubbingStore()
const useCommerDubbing = useCommerDubbingStore()

//弹窗
const musicDialogVisible = ref(false)
// 监听弹窗关闭，就关闭所有音乐播放
watch(() => musicDialogVisible.value, (newVal) => {
  console.log('newVal',newVal)
    
  if(newVal==false){
    musiceList.value.map((child,idx)=>{
      musiceList.value[idx].isPauseTtsAudio = true
      audio_ref.value[idx]&&audio_ref.value[idx].hide_volume_bar()
    })
  }else{
    userId.value=JSON.parse(localStorage.getItem('user'))?.userId || ''
    nextTick(()=>{
      gradientDivs.value.forEach(el => {
        if (el) animateAngle360(el, 10000); // 10秒转一圈
      });
    })
  }
})
let volume=ref()
const eventBus = inject('eventBusMusic');
const open = (slideValue) => {
  if(useCommerDubbing.progress_barNum==3){
    useCommerDubbing.bgmusic_volume=slideValue
    
   }else{
    useAIDubbing.bgmusic_volume=slideValue
   }
   volume=slideValue
  // console.log('Opening modal with title:');
  // 打开模态框的逻辑
  musicDialogVisible.value = true;
};

const closeLetter = () => {
  // console.log('Closing modal');
  // 关闭模态框的逻辑
};

// 监听主页面中音乐标签是否关闭，如果关闭，取消所有选中状态
watch(()=>Object.keys(useAIDubbing.bgmusicObj).length,(newValue,oldValue)=>{
  // console.log(newValue)
  if(newValue==0){
    musiceList.value.map((child,idx)=>{
        child.isSelected = false
    })
  }
})


let audio_ref=ref([])
// 音乐列表还有标签列表
const musiceList = ref()
const musiceListTabs = ref()

// 列表中item点击事件
const musiceListItemIs_actie = ref(-1)
const click_item = (item,index)=>{
  musiceListItemIs_actie.value = index

}

// 标签点击事件
const musiceListTabsNum = ref(0)
const click_tabs = (index)=>{
  // console.log(index)
  musiceListItemIs_actie.value = -1
  musiceListTabsNum.value = index
  // console.log(musiceListTabs.value[index].bgmList)
  if(index==0&&userId.value==''){
    musiceList.value = []
  }else{
    musiceList.value = musiceListTabs.value[index].bgmList
  }
 
  // console.log('pppp',musiceList.value)
}

// 点击列表中的选用按钮
// 选中要播放的音乐，合成音频时使用
// const select_musicUrl = ref('')

const select_adopt=(item,index)=>{
  // // storagePath  ossPath
  // console.log(item)
  // 第四部显示的背景音乐赋值
  if(useCommerDubbing.progress_barNum==3){
    useCommerDubbing.bgmusicObj = item
    useCommerDubbing.bgmusic_url = item.storagePath || item.ossPath
  }else{
    useAIDubbing.bgmusicObj = item
    // select_musicUrl.value = item.storagePath || item.ossPath
    useAIDubbing.bgmusic_url = item.storagePath || item.ossPath
    
  }


  musiceList.value.map((child,idx)=>{
    if((item.materialId || item.id)==( child.materialId || child.id )){
      musiceList.value[idx].isSelected = true
    }else{
      musiceList.value[idx].isSelected = false
    }
  })
  musicDialogVisible.value = false
}


// 获取背景音乐列表方法
const getBGMIFun=async ()=>{
  await getBGMIApi({
    userId:userId.value
  }).then(res=>{
    // console.log(res)
    if(res.code==0){
      let { audioMaterials,themeGroups } = res.data
      themeGroups.map(item=>{
        item.bgmList.map(child=>{
          child.color = getRandomColor()
          child.color1 = getRandomColor()
          child.color2 = getRandomColor()
          child.isSelected = false
          child.isPauseTtsAudio = false
        })
      })
      audioMaterials.map(item=>{
        item.color = getRandomColor()
        item.color1 = getRandomColor()
        item.color2 = getRandomColor()
        item.isSelected = false
        item.isPauseTtsAudio = false
      })
      // console.log('ooo',themeGroups)
      // console.log('oipiop',audioMaterials)

      musiceListTabs.value = themeGroups
      musiceListTabs.value.unshift({
        theme: '我的',
        bgmList: audioMaterials
      });
      musiceList.value = musiceListTabs.value[0].bgmList
      // console.log('pppp',musiceList.value)
    }else{
      musiceListTabs.value = []
      musiceList.value = []
    }
    
  }).catch(err=>{
    musiceListTabs.value = []
    musiceList.value = []
    console.log(err)

  })
}

// 监听音乐某个列表项是否播放,只可以播放一个
const childEvent=(item,index)=>{
  musiceList.value.map((child,idx)=>{
    if((item.materialId || item.id)==( child.materialId || child.id )){
      musiceList.value[idx].isPauseTtsAudio = false
    }else{
      musiceList.value[idx].isPauseTtsAudio = true
    }
  })
}


// 获取的文件上传路径
const fileUrl = ref('')
const signatureData = ref({})
// 上传文件之前获取oss签名
const getSignatureFun=async (file)=>{
  try {
    const response = await get_signature({ userId:userId.value, fileType:file.type})
    let { code , data } = response
    if (code !== 0) {
      // console.log('获取签名失败')
      return
    }
    signatureData.value = data
    // const fileUrl = `${response.host}/${response.key}?Expires=${response.expire}&OSSAccessKeyId=${response.accessid}&Signature=${encodeURIComponent(response.signature)}`;
    fileUrl.value = `${data.host}/${data.key}?Expires=${data.expire}&OSSAccessKeyId=${data.accessid}&Signature=${data.signature}`
    // console.log('pppopop',fileUrl)
    // console.log('response',response)
  } catch (err) {
    console.log(err)
  } finally {

  }
}

const uploadFile = async (file) => {
  const formData = new FormData();
  formData.append('OSSAccessKeyId', signatureData.value.accessKeyId);
  formData.append('policy', signatureData.value.policy);
  formData.append('signature', signatureData.value.signature);
  // formData.append('key', `${signatureData.value.dir}/${file.name}`);
  formData.append('key', `${signatureData.value.key.replace(/[^\/]+$/, '')}${file.name}`);

  formData.append('file', file);

  // 3. 发送 POST 请求
  await axios.post(signatureData.value.host, formData,{
    headers: { 'Content-Type': 'multipart/form-data' }
  });
};

// 添加获取当前实例
const { proxy } = getCurrentInstance();

// 添加登录检查函数
const checkUserLogin = () => {
	// 从本地存储获取user信息
	const userStorage = localStorage.getItem('user');
	if (!userStorage) return false;
	
	try {
		const userData = JSON.parse(userStorage);
		// 检查token是否为null或空
		return userData && userData.token && userData.token.trim() !== '';
	} catch (e) {
		console.error('解析用户数据失败:', e);
		return false;
	}
};

// 上传文件前校验
const beforeUpload=async (file)=>{
  console.log(file,'beforeUpload',);
  
  // 检查用户是否已登录
  if (!checkUserLogin()) {
    // 未登录，弹出登录弹窗
    proxy.$modal.open('组合式标题');
    return false;
  }
  
  // console.log('544545',file)
  await getSignatureFun(file)
  const allowedExtensions = ['mp3', 'wav'];
  const fileExtension = file.name.split('.').pop().toLowerCase();
  const isValidExtension = allowedExtensions.includes(fileExtension);
  // const isValidSize = file.size <= 10 * 1024 * 1024;
  if (!isValidExtension) {
    this.$message.error('仅支持 MP3/WAV 格式！');
    return false;
  }
  // if (!isValidSize) {
  //   this.$message.error('文件大小不能超过 10MB！');
  //   return false;
  // }
  // console.log('ooooooooooooooooooooooooooooo')
  return true;
}
const handleOssUpload = async (options) => {
  console.log(options,'handleOssUpload');
  
  // 检查用户是否已登录
  if (!checkUserLogin()) {
    // 未登录，弹出登录弹窗
    proxy.$modal.open('组合式标题');
    return;
  }
  
  // console.log('pppppppppppppppppppp')
  try {
    const { file } = options;
    const fileNameWithoutExt = file.name.split('.').slice(0, -1).join('.')
    // console.log('ppp',file)
    const data = await uploadFile(file)
    // console.log('data',data)
    upload_callback({
      userId: userId.value,
      materialName: fileNameWithoutExt,
      ossPath: signatureData.value.key.replace(/[^\/]+$/, '') + file.name,
      fileSize: String(file.size),
      fileExtension: file.name.split('.').pop(),
      tagNames: '1',
      materialType: 'audio',
      isPrivate: '1',
      // storage_path: `/material/${userId}/${file.name}`
      storage_path: `/material/${userId.value}/${file.name}`,
      music_classification:'bgm',
    }).then(res=>{
      // console.log('response46546546',res)
      if(res.code==0){
        ElMessage({
          message: '上传成功',
          type: 'success',
        })
        getBGMIFun()
        uploadRef.value.clearFiles()
 
      }else{
        ElMessage({
          message: '上传失败',
          type: 'error',
        })
      }
    }).catch(err=>{
      // console.log('46546',err)
    })

  } catch (error) {
    console.error('上传失败：', error);
  }
};












// 随机渐变色
function getRandomColor() {
  const R = Math.floor(Math.random() * 130 + 110); // 110-239范围
  const G = Math.floor(Math.random() * 130 + 110);
  const B = Math.floor(Math.random() * 130 + 110);
  return `rgb(${R},${G},${B})`;
}
let init=()=>{
   userId.value=JSON.parse(localStorage.getItem('user'))?.userId || ''
  getBGMIFun()
  eventBus.on('openMusicModal', open);
  eventBus.on('closeMusicModal', closeLetter);
}
onMounted(() => {
  init()
});
let initialized = false;
watch(() => loginStore.token, (newVal, oldVal) => {
  if (!initialized) {
    initialized = true;
    return;  // 第一次执行跳过刷新
  }
  if (newVal) {
    init()
  }
}, { immediate: true,deep:true });
onUnmounted(() => {
  eventBus.off('openMusicModal', open);
  eventBus.off('closeMusicModal', closeLetter);
});

// 中间滚动事件
const scroll = ref(null);
let throttle=(func, delay)=>{
  let last = 0;
  return function (...args) {
    const now = Date.now();
    if (now - last >= delay) {
      last = now;
      func.apply(this, args);
    }
  };
}

const handleScroll = (e) => {
  // console.log(e)
  const wrapRef = scroll.value.wrapRef;
  // console.log('wrapRef.scrollHeight',wrapRef.scrollHeight)   //内容总高度560
  // console.log('wrapRef.clientHeight',wrapRef.clientHeight)    //可视区域高度300
  // console.log('event.scrollTop',e.scrollTop)    //// 滚动条距盒子顶部高度260
  let poor = wrapRef.scrollHeight - wrapRef.clientHeight;
  // 判断滚动到底部
  if (e.scrollTop  >= poor) {
    console.log('ppppppp到底了')
  }
}
let throttledScroll = throttle(handleScroll, 100);
let setAudioRef=(el, index)=>{
  audio_ref.value[index] = el;
}
let gradientDivs = ref([]);
let hasInsertedKeyframes =ref(false);
// 返回渐变背景样式，角度用CSS变量控制
const insertKeyframes = () => {
  if (hasInsertedKeyframes.value) return;
  const style = document.createElement('style');
  style.innerHTML = `
    @keyframes neonGradient {
      0% {
        background-position: 0% 100%;
      }
      25% {
        background-position: 50% 75%;
      }
      50% {
        background-position: 100% 0%;
      }
      75% {
        background-position: 50% 25%;
      }
      100% {
        background-position: 0% 100%;
      }
    }
  `;
  document.head.appendChild(style);
  hasInsertedKeyframes.value = true;
};

const gradientStyle = (color, color1, color2, duration = '10s') => {
  insertKeyframes();
  return {
    background: `linear-gradient(
      var(--angle, 0deg),
      ${color} 0%,
      ${color} 25%,
      ${color1} 40%,
      ${color1} 60%,
      ${color2} 75%,
      ${color2} 100%
    )`,
    backgroundSize: '200% 200%',
    animation: `neonGradient ${duration} cubic-bezier(0.4, 0, 0.2, 1) infinite`,
  };
};

// 360度匀速旋转角度动画
const animateAngle360 = (el, duration = 10000) => {
  let startTime = null;

  const step = (timestamp) => {
    if (!startTime) startTime = timestamp;
    const elapsed = timestamp - startTime;
    // 计算角度，0~360度循环
    const angle = (elapsed / duration) * 360 % 360;
    el.style.setProperty('--angle', angle + 'deg');
    requestAnimationFrame(step);
  };

  requestAnimationFrame(step);
};
let changeVolume=(data)=>{
  console.log(data,'改变音量');
   if(useCommerDubbing.progress_barNum==3){
    useCommerDubbing.bgmusic_volume=Math.round(data*100)
   }else{
    useAIDubbing.bgmusic_volume=Math.round(data*100)
   }
}
</script>

<template>
  <Teleport to="body">
    <el-dialog v-model="musicDialogVisible" width="700" center append-to="#app">
      <div class="dialog_content flex flex_d-column flex_a_i-center">
        <span class="text-align-center height-25 font-size-14">背景音乐</span>

        <div class="dialog_content_middle1 height-40 width-full flex flex_a_i-center flex_j_c-space-between margin_b-10">
          <ul class="dialog_content_middle1_ul flex">
            <li class="margin_r-60 height-20 flex flex_a_i-center cursor-pointer" :class="{'is_active':musiceListTabsNum==index}"
                v-for="(item,index) in musiceListTabs" :key="item.theme" @click="click_tabs(index)">
              {{ item.theme }}</li>
          </ul>
<!--          <div class="dialog_content_middle1_right font-size-14 flex flex_a_i-center">-->
<!--            <el-dropdown trigger="click">-->
<!--              <span class="el-dropdown-link cursor-pointer">-->
<!--                更多-->
<!--                <el-icon class="el-icon&#45;&#45;right">-->
<!--                  <Iconfont-->
<!--                      size="12px"-->
<!--                      name="jiantou"-->
<!--                  />-->
<!--                </el-icon>-->
<!--              </span>-->
<!--              <template #dropdown>-->
<!--                <el-dropdown-menu>-->
<!--&lt;!&ndash;                  <el-dropdown-item :icon="Plus">Action 1</el-dropdown-item>&ndash;&gt;-->
<!--&lt;!&ndash;                  <el-dropdown-item :icon="CirclePlusFilled">&ndash;&gt;-->
<!--&lt;!&ndash;                    Action 2&ndash;&gt;-->
<!--&lt;!&ndash;                  </el-dropdown-item>&ndash;&gt;-->
<!--                  <el-dropdown-item :icon="CirclePlus">Action 3</el-dropdown-item>-->
<!--                  <el-dropdown-item :icon="Check">Action 4</el-dropdown-item>-->
<!--                  <el-dropdown-item :icon="CircleCheck">Action 5</el-dropdown-item>-->
<!--                </el-dropdown-menu>-->
<!--              </template>-->
<!--            </el-dropdown>-->
<!--          </div>-->
        </div>

        <el-scrollbar class="width-full" heigth="500" @scroll="throttledScroll" ref="scroll">
          <el-row :gutter="20"  class="list_row width-full cursor-pointer" style="max-height: 300px">
            <el-col :span="12" class="list_col margin_b-10 flex" v-if="musiceListTabsNum==0&&userId!=''">
<!--              <div class="list_item height-80 flex flex_a_i-center flex_j_c-center overflow-hidden">-->
<!--                <div class="list_item_item height-78 flex flex_a_i-center">-->
<!--                  <img src="@/assets/images/aiImages/uploadImg.png" alt="" class="width-50 height-50 margin-n-5">-->
<!--                  <span class="font-size-14">上传背景音乐</span>-->
<!--                </div>-->
<!--              </div>-->
<!--             -->
              <el-upload
                  ref="uploadRef"
                  action="#"
                  class="upload-demo width-full"
                  :auto-upload="true"
                  :http-request="handleOssUpload"
                  :before-upload="beforeUpload"
                  :show-file-list="false"
         
                  accept=".mp3, .wav, audio/*"
              >
                <template #trigger>
                  <div class="list_item height-80 flex flex_a_i-center flex_j_c-center overflow-hidden width-full">
                    <div class="list_item_item height-78 flex flex_a_i-center">
                      <img src="@/assets/images/aiImages/uploadImg.png" alt="" class="width-50 height-50 margin-n-5">
                      <span class="font-size-14">上传背景音乐</span>
                    </div>
                  </div>
                </template>
<!--                <el-button class="ml-3" type="success">-->
<!--                  upload to server-->
<!--                </el-button>-->

<!--                <template #tip>-->
<!--                  <div class="el-upload__tip">-->
<!--                    jpg/png files with a size less than 500kb-->
<!--                  </div>-->
<!--                </template>-->
              </el-upload>
            </el-col>
            <el-col  :span="24" class="list_col margin_b-10 flex" v-if="musiceListTabsNum==0&&userId==''">
              <div data-v-4c8acc69="" class="error-state">
                <div class="error-icon"></div>
                <div class="error-text">请先登录后查看您的音乐</div>
              </div>
            </el-col>
            <el-col :span="12" class="list_col margin_b-10 flex" v-for="(item,index) in musiceList" :key="item.id" v-else>
              <div class="list_item height-80  flex flex_a_i-center flex_j_c-center "
                   :class="{'is_active':musiceListItemIs_actie==index}" @click="click_item(item,index)">
                <div class="list_item_item height-78 flex flex_a_i-center">
<!--                  <img src="@/assets/images/aiImages/uploadImg.png" alt="" class="width-50 height-50 margin-n-5">-->
                  <div class="width-50 height-50 margin-n-5 " style="border-radius: 40px;flex-shrink: 0;"
                       :ref="el => gradientDivs[index] = el"
                       :style="gradientStyle(item.color,item.color1,item.color2)"
                  ></div>
                  <div class="list_item_item_right flex flex_d-column flex-item_f-2">
                    <span class="list_item_item_right_span1 font-size-14 ellipse">{{ item?.materialName || item?.musicName }}</span>
                    <span class="list_item_item_right_span2 font-size-11 margin-5-n ellipse "></span>
                   <!-- <div class="list_item_item_right_div width-60 height-20 cursor-pointer flex flex_a_i-center">
                     <Iconfont
                         class="margin-n-2"
                         color="#0AAF60"
                         size="14px"
                         name="bofang1"
                     />
                     <span class="font-size-12">04:30</span>
                   </div> -->
                    <audioPlay :audioUrl="item.ossPath || item.storagePath" :isPauseTtsAudio="item.isPauseTtsAudio" @changeVolume="changeVolume" :volume="volume" 
                               :index="String(index)" :item="item" @child-event="childEvent(item,index)" @showVolume="showVolume"  :ref="el => setAudioRef(el, index)"
                    ></audioPlay>
                  </div>
                  <div class="width-50 height-25 font-size-12 margin-n-4 flex flex_a_i-center flex_j_c-center cursor-pointer
                        list_item_item_right_right" :class="{'active':item.isSelected==true}"
                        @click.stop="select_adopt(item,index)"
                  >选用</div>
                </div>
              </div>
            </el-col>
          
          </el-row>
        </el-scrollbar>
      </div>
    </el-dialog>
  </Teleport>
</template>

<style scoped lang="scss">
.el-upload-dragger .el-upload__text {
  color: var(--el-text-color-regular);
  font-size: 14px;
  text-align: center;
  line-height: 20px;
}

::v-deep(.el-upload--text){
  width: 100%;
}


.dialog_content{
  //background-color: #006eff;
  span{
    font-weight: 500;
    color: #121212;
  }
  &_middle1{
    &_ul{
      li{
        color: #5B5555;
      }
      .is_active{
        color:#0AAF60;
        border-bottom: 2px solid #0AAF60;
      }
    }
    &_right{
      color: #7E838D;
      //background-color: #67c23a;
      .el-dropdown-link{
        color: #7E838D;
      }
    }
  }

  .list_row{
    .list_col{
      .list_item{
        background-color:#F7F7F9;
        border-radius: 12px;
        &_item{
          width: 99%;
          border-radius: 12px;
          background-color: #fff;
          &_right{
            &_span1{
              //display: block;
              //color: #121212;
              //text-overflow: ellipsis;
              //overflow: hidden;
              //word-break: break-all;
              //width: 90%;
            }
            &_span2{
              color: #7E838D;
            }
            &_div{
              background: #F7F7F9;
              border-radius: 15px;
              span{
                color: #7E838D;
              }
            }
          }
          .list_item_item_right_right{
            background: #F7F7F9;
            border-radius: 14px;
            font-weight: 400;
            color: #A0A3B7;
          }
          .active{
            background: rgba(10,175,96,0.06);
            color: #0AAF60;
          }

        }

      }
      .is_active{
        background-image: linear-gradient(134deg, rgba(10, 175, 96, 1), rgba(255, 214, 0, 1));
      }
      .error-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 200px;

          .error-icon {
            width: 40px;
            height: 40px;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23F56C6C'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/%3E%3C/svg%3E");
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            margin-bottom: 16px;
          }

          .error-text {
            color: #F56C6C;
            font-size: 14px;
            margin-bottom: 16px;
          }
			  } 
    }
  }








}

@keyframes neonGradient {
  0% {
    background-position: 0% 100%;
  }
  25% {
    background-position: 50% 75%;
  }
  50% {
    background-position: 100% 0%;
  }
  75% {
    background-position: 50% 25%;
  }
  100% {
    background-position: 0% 100%;
  }
}

</style>