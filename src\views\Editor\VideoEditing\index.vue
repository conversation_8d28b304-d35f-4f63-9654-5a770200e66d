<template>
	<div class="video-editing-container">
		<!-- 顶部标题栏：显示页面标题 -->
		<Headbar />

		<!-- 操作栏：包含新建、最近、编辑、导出等操作 -->
		<OperationBar @action="handleBarAction" />

		<!-- 主要内容区域：包含左侧编辑区和右侧预览区 -->
		<div class="main-content">
			<!-- 左侧区域：包含菜单和工具区 -->
			<div class="left-section">
				<!-- 左侧菜单：导航菜单 -->
				<LeftMenu />

				<!-- 视频剪辑工具区域：包含工具栏和视频内容 -->
				<div class="video-editing-tools">
					<!-- 工具栏：包含视频成片、预览、匹配设置等工具 -->
					<div class="tools-wrapper">
						<div class="tools-container">
							<div class="tools-bar">
								<!-- 循环渲染工具栏按钮 -->
								<div v-for="tool in tools" :key="tool.id" class="tool-item"
									:class="{ active: activeTool === tool.id }" @click="handleToolClick(tool)">
									<!-- 工具图标，根据激活状态显示不同图片 -->
									<img :src="(activeTool === tool.id || hoveredTool === tool.id) ? tool.activeImg : tool.img"
										:alt="tool.name" class="tool-icon" @mouseenter="hoveredTool = tool.id"
										@mouseleave="hoveredTool = null">
								</div>
							</div>
						</div>
					</div>

					<!-- 视频上传区域：根据选择的工具显示不同内容 -->
					<div class="video-content">
						<!-- 添加提取内容区域：在视频成片工具内容中添加 -->
						<div v-if="activeTool === 1">
							<div class="match-content">
								<!-- 修改模式选择按钮顺序，将指定视频放在前面 -->
								<div class="match-buttons">
									<button class="btn-tab" :class="{ active: matchMode === 2 }"
										@click="setStyleMode">指定视频</button>
									<button class="btn-tab" :class="{ active: matchMode === 1 }"
										@click="setAlbumMode">指定专辑</button>
								</div>

								<!-- 同时调整内容显示的条件，保持对应关系 -->
								<!-- 指定视频模式内容 -->
								<div v-if="matchMode === 2" class="style-content">
									<!-- 筛选区域：包含日期、资源库选择和全选功能 -->
									<div class="filter-bar">
										<!-- 创建时间筛选 -->
										<div class="filter-item">
											<span class="filter-label">创建时间</span>
											<el-date-picker v-model="dateRange" type="daterange" range-separator=" - "
												start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD"
												value-format="YYYY-MM-DD" size="small" :clearable="false"
												style="width: 180px;" :locale="zhCn" popper-class="date-picker-small" />
										</div>
										<!-- 资源库筛选：选择专辑 -->
										<div class="filter-item">
											<span class="filter-label">资源库</span>
											<el-select v-model="selectedSource" placeholder="请选择专辑" size="small"
												style="width: 100px;" @change="handleSourceChange">
												<!-- 资源库选项 -->
												<el-option v-for="item in sourceOptions" :key="item.value"
													:label="item.label" :value="item.value" />
											</el-select>
										</div>
										<!-- 全选功能和选中数量显示 -->
										<!-- <div class="selection-count">
											<label class="select-all">
												<input type="checkbox" v-model="isAllSelected"
													@change="toggleSelectAll">
												<span>全选</span>
											</label>
											<span class="count">{{ selectedCount }}/{{ videoMaterials.length }}</span>
										</div> -->
									</div>

									<!-- 视频素材网格：显示视频列表 -->
									<div class="video-grid">
										<!-- 上传素材按钮 -->
										<div class="video-upload-item" @click="!isUploading && handleUploadClick()">
											<!-- 隐藏的文件输入框 -->
											<input type="file" ref="fileInput" style="display: none"
												accept="video/*,.mp4" @change="handleFileChange" />
											<!-- 上传按钮图标（非上传状态） -->
											<div v-if="!isUploading" class="upload-icon">
												<span class="plus-icon">+</span>
											</div>
											<div v-if="!isUploading" class="upload-text">上传素材</div>

											<!-- 上传进度显示（上传状态） -->
											<template v-if="isUploading">
												<div class="upload-progress">
													<div class="progress-info">
														<div class="file-name">{{ uploadFile.name }}</div>
														<div class="progress-percent">{{ uploadFile.percent }}%</div>
													</div>
													<div class="progress-bar-bg">
														<div class="progress-bar"
															:style="{ width: uploadFile.percent + '%' }"></div>
													</div>
													<div class="cancel-upload" @click.stop="cancelUpload">取消</div>
												</div>
											</template>
										</div>

										<!-- 视频素材项：循环渲染所有视频 -->
										<div class="video-item" v-for="(video, index) in videoMaterials" :key="index"
											@click="handleVideoPreview(video)">
											<!-- 视频缩略图 -->
											<div class="video-thumbnail" :style="{
												backgroundImage: video.thumbnailUrl ? `url(${video.thumbnailUrl})` : 'none',
												backgroundSize: 'cover',
												backgroundPosition: 'center',
												backgroundColor: video.thumbnailUrl ? 'transparent' : '#333'
											}">
												<!-- 选择指示器：显示选中状态和序号 -->
												<div class="select-indicator" :class="{ active: video.selected }"
													@click.stop="toggleVideoSelection(video, index)">
													<!-- 替换文字为图片，根据选中状态显示不同图片 -->
													<img v-if="video.selected" src="@/assets/img/xxx.png" alt="Selected"
														class="select-icon" />
													<img v-else src="@/assets/img/www.png" alt="Not Selected"
														class="select-icon" />
												</div>
											</div>
											<!-- 视频标题 -->
											<div class="video-title">{{ video.title }}</div>
										</div>
									</div>

									<!-- 智能匹配按钮：启动视频生成 -->
									<div class="match-button-container">
										<button class="smart-match-button" @click="handleSmartMatch">智能匹配</button>
									</div>
								</div>

								<!-- 指定专辑模式内容 -->
								<div v-else-if="matchMode === 1" class="material-grid">
									<!-- 日期筛选区域 -->
									<div class="filter-bar">
										<div class="filter-item">
											<span class="filter-label">创建时间</span>
											<!-- 日期范围选择器 -->
											<el-date-picker v-model="dateRange" type="daterange" range-separator=" - "
												start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD"
												value-format="YYYY-MM-DD" size="small" :clearable="false"
												style="width: 220px;" :locale="zhCn" popper-class="date-picker-small" />
										</div>
									</div>

									<!-- 专辑列表容器 -->
									<div class="albums-container">
										<!-- 循环渲染专辑项 -->
										<div class="material-item" v-for="(item, index) in animationMaterials"
											:key="index" :class="{
												'selected': selectedAlbumItem && selectedAlbumItem.id === item.id ||
													selectedAlbumItem && selectedAlbumItem.tagId === item.tagId
											}" @click="selectMaterial(item)">
											<div class="material-placeholder">{{ item.name }}</div>
										</div>
									</div>

									<!-- 添加智能匹配按钮，与指定视频页面样式一致 -->
									<div class="album-match-button-container">
										<button class="smart-match-button" @click="handleSmartMatch">智能匹配</button>
									</div>
								</div>
							</div>
						</div>

						<!-- 预览工具内容：为activeTool=2时显示 -->
						<div v-else-if="activeTool === 2" class="preview-content-tool">
							<!-- 添加加载效果覆盖层 -->
							<div v-if="isLoadingPreview" class="preview-loading-overlay">
								<div class="loading-spinner"></div>
								<div class="loading-text">正在加载预览视频，请稍候...</div>
							</div>

							<!-- 现有的预览内容 -->
							<!-- 视频缩略图列表 -->
							<div class="video-thumbnails-list-container">
								<!-- 左侧滑动按钮 -->
								<div class="thumbnail-scroll-left" @click="scrollThumbnails('left')"
									v-show="isLeftScrollVisible">
									<i>‹</i>
								</div>



								<!-- 视频缩略图列表：可横向滚动 -->
								<div class="video-thumbnails-list" ref="thumbnailsListRef">
									<!-- 循环渲染视频缩略图 -->
									<div v-for="(item, index) in thumbnailVideos" :key="index" class="thumbnail-item"
										:class="{ active: activeVideoIndex === index }" @click="selectVideo(index)">
										<!-- 缩略图显示 -->
										<div class="thumbnail-image" :style="{
											backgroundColor: !item.url && !item.thumbnailUrl ? (item.color || '#333') : 'transparent',
											backgroundImage: item.thumbnailUrl
												? `url(${item.thumbnailUrl})`
												: (item.url ? `url(${item.url})` : 'none'),
											backgroundSize: 'cover',
											backgroundPosition: 'center'
										}">
											<!-- 添加阴影层和标题 -->
											<div class="thumbnail-overlay">
												<span class="thumbnail-title">{{ item.title }}</span>
											</div>
										</div>
									</div>
								</div>

								<!-- 右侧滑动按钮 -->
								<div class="thumbnail-scroll-right" @click="scrollThumbnails('right')"
									v-show="isRightScrollVisible">
									<i>›</i>
								</div>
							</div>

							<!-- 主视频播放区域 -->
							<div class="main-video-player">
								<div class="video-container">
									<!-- 视频播放器 -->
									<video ref="videoPlayer" class="video-element" controls>
										<source src="" type="video/mp4">
										您的浏览器不支持 HTML5 视频。
									</video>
								</div>
							</div>

							<!-- 视频操作按钮区域 -->
							<div class="video-action-buttons">
								<!-- 左侧按钮组：下载视频、云剪辑 -->
								<div class="left-buttons">
									<button class="btn-action btn-white btn-outline-green" @click="downloadVideo">
										<img src="@/assets/img/btn.png" alt="下载视频" class="btn-icon download-icon">
										<span>下载视频</span>
									</button>
									<!-- <button class="btn-action btn-white btn-outline-green">
										<img src="@/assets/img/跳转云剪辑.png" alt="云剪辑" class="btn-icon">
										<span>云剪辑</span>
									</button> -->
								</div>
								<!-- 右侧按钮组：加入空间 -->
								<!-- <div class="right-buttons">
									<button class="btn-action btn-green" @click="addToSpace">
										<img src="@/assets/img/加入空间.png" alt="加入空间" class="btn-icon">
										<span>加入空间</span>
									</button>
								</div> -->
							</div>
						</div>

						<!-- 匹配设置工具内容：为activeTool=3时显示 -->
						<div v-else-if="activeTool === 3" class="match-settings-content">
							<!-- 设置标题 -->
							<h3 class="settings-title">设置项</h3>
							<!-- 设置表单 -->
							<el-form :model="settingsForm" class="settings-form">
								<!-- 输出数量设置 -->
								<el-form-item>
									<div class="input-item">
										<span class="input-label">输出数量</span>
										<div class="input-control-wrap">
											<el-input-number v-model="settingsForm.inputCount" :min="1" :max="100"
												size="small" class="count-input" :controls-position="'right'"
												:controls="true" type="number"></el-input-number>
											<span class="input-tip">输出范围1-100</span>
										</div>
									</div>
								</el-form-item>

								<!-- 输出尺寸设置 -->
								<el-form-item>
									<div class="input-item">
										<span class="input-label">输出尺寸 (宽*高)</span>
										<div class="size-inputs-container">
											<div class="size-inputs">
												<el-input v-model="settingsForm.width" placeholder="宽度"
													class="size-input"></el-input>
												<span class="size-separator">×</span>
												<el-input v-model="settingsForm.height" placeholder="高度"
													class="size-input"></el-input>
											</div>
											<div class="input-tip size-tip">宽高都不能小于128px，宽高都不能大于4096px，短边不能大于2160px
											</div>
										</div>
									</div>
								</el-form-item>

								<!-- 允许添加特效开关 -->
								<el-form-item>
									<div class="input-item">
										<span class="input-label">允许添加特效</span>
										<div class="size-inputs-container">
											<div class="switch-wrapper">
												<el-switch v-model="settingsForm.enableEffects" active-color="#0AAF60"
													inactive-color="#DCDFE6" class="custom-switch"></el-switch>
											</div>
											<div class="input-tip size-tip">是否允许添加特效效果</div>
										</div>
									</div>
								</el-form-item>

								<!-- 自动存储至云空间开关 -->
								<el-form-item>
									<div class="input-item">
										<span class="input-label">自动存储至云空间</span>
										<div class="size-inputs-container">
											<div class="switch-wrapper">
												<el-switch v-model="settingsForm.autoSaveToCloud" active-color="#0AAF60"
													inactive-color="#DCDFE6" class="custom-switch"></el-switch>
											</div>
										</div>
									</div>
								</el-form-item>

								<!-- 视频素材音量滑块 -->
								<el-form-item>
									<div class="input-item horizontal-slider-item">
										<span class="input-label">视频素材音量</span>
										<div class="slider-wrapper">
											<el-slider v-model="settingsForm.materialVolume" :min="0" :max="100"
												:step="1" :show-tooltip="true" @change="handleMaterialVolumeChange"
												class="custom-slider"></el-slider>
										</div>
									</div>
								</el-form-item>

								<!-- 原始素材音量设置（已注释掉的功能） -->
								<!-- <el-form-item>
									<div class="input-item horizontal-slider-item">
										<span class="input-label">原始素材音量设置</span>
										<div class="slider-wrapper">
											<el-slider v-model="settingsForm.originalVolume" :min="0" :max="100"
												:step="1" :show-tooltip="true" @change="handleVolumeChange"
												class="custom-slider"></el-slider>
										</div>
									</div>
								</el-form-item> -->

								<!-- 为背景配乐开关（已注释掉的功能） -->
								<!-- <el-form-item>
									<div class="input-item">
										<span class="input-label">为背景配乐</span>
										<div class="size-inputs-container">
											<div class="switch-wrapper">
												<el-switch v-model="settingsForm.backgroundMusic" active-color="#0AAF60"
													inactive-color="#DCDFE6" class="custom-switch"></el-switch>
											</div>
											<div class="input-tip size-tip">仅智能对话和配音模式生效，真实出片内容以片子对应为主</div>
										</div>
									</div>
								</el-form-item> -->

								<!-- 保存和取消按钮 -->
								<div class="form-buttons">
									<el-button type="success" @click="saveSettings">保存</el-button>
									<el-button @click="cancelSettings">取消</el-button>
								</div>
							</el-form>
						</div>
					</div>
				</div>
			</div>

			<!-- 右侧预览区域：使用PreviewPanel组件显示预览内容 -->
			<PreviewPanel ref="previewPanel" :isVideoEditingPage="true" :extractedContent="extractedContentList"
				:activeToolId="activeTool" @generate-video="handleGenerateVideo" @add-role="handleAddRole"
				@add-music="handleAddMusic" @add-video="handleAddVideo"
				@cancel-video-selection="handleCancelVideoSelection" @volume-change="handleVolumeChange"
				@switchToPreview="switchToPreviewTool" @extractedItemClick="handlePreviewExtractedItemClick" />
		</div>

		<!-- 各种对话框组件 -->
		<!-- 素材对话框：用于选择视频素材 -->
		<MaterialDialog :visible="showVideoDialog" :material-list="materialDialogList"
			:title-icon="videoDialogTitleIcon" :multiple="false" @update:visible="showVideoDialog = $event"
			@close="closeVideoDialog" @confirm="handleVideoDialogConfirm" @remove="handleVideoDialogRemove"
			@toggle-play="handleVideoDialogTogglePlay" @upload="handleVideoDialogUpload" />

		<!-- 视频预览弹窗：用于预览视频 -->
		<VideoPreviewDialog v-model:visible="previewDialogVisible" :video-url="currentPreviewUrl" />

		<!-- 添加音乐对话框：用于添加背景音乐 -->
		<MusicDialog v-model:visible="musicDialogVisible" :material-list="previewMusicList"
			@close="musicDialogVisible = false" @confirm="handleMusicDialogConfirm" @remove="handleMusicDialogRemove"
			@togglePlay="handleMusicDialogTogglePlay" @add-music="handleMusicDialogAddMusic" />

		<!-- 视频加载对话框：显示视频生成进度 -->
		<VideoLoadingDialog v-model:visible="loadingDialogVisible" :progress="loadingProgress" :videos="loadingVideos"
			@close="handleLoadingDialogClose" />
		<!-- 确保传递缩略图数据给对话框 -->

		<!-- 加入空间对话框 -->
		<SubtitleDialog v-model="spaceDialogVisible" :folders="spaceFolders" :selected-folders="selectedSpaceFolders"
			:sourcePage="'ai-tool'" @confirm="handleSpaceDialogConfirm" />

		<!-- 添加确认弹窗 -->
		<AlertDialog v-model:visible="confirmDialogVisible" type="warning" title="" :message="confirmDialogMessage"
			confirm-button-text="继续" cancel-button-text="取消" :show-cancel-button="true" :custom-confirm-class="true"
			:custom-cancel-class="true" :show-fee-explanation="true" @confirm="handleConfirmMatchAction"
			@feeExplanation="handleFeeExplanation" />

		<!-- 添加扣费说明弹窗 -->
		<AlertDialog v-model:visible="feeExplanationDialogVisible" type="info" title="" :message="feeExplanationMessage"
			confirm-button-text="确认" :show-cancel-button="false" :custom-confirm-class="true"
			:show-fee-explanation="false" :show-icon="false" />

		<!-- 添加算力不足警告弹窗 -->
		<AlertDialog v-model:visible="insufficientComputeDialogVisible" type="warning" title=""
			:message="insufficientComputeMessage" confirm-button-text="立即购买" cancel-button-text="暂不购买"
			:show-cancel-button="true" :custom-confirm-class="true" :custom-cancel-class="true"
			:show-fee-explanation="false" @confirm="handleBuyCompute" />

		<!-- 添加空间不足对话框 -->
		<AlertDialog v-model:visible="insufficientSpaceDialogVisible" type="warning" :title="''"
			:sub-title="insufficientSpaceTitle" :message="insufficientSpaceMessage" confirm-button-text="立即购买"
			cancel-button-text="暂不购买" :show-cancel-button="true" :custom-confirm-class="true"
			:custom-cancel-class="true" :show-fee-explanation="false" @confirm="handleBuySpace"
			@cancel="handleCancelBuy" />

	</div>
</template>

<script setup>
// 导入Vue相关功能
import { ref, reactive, onMounted, nextTick, computed, provide, watch, getCurrentInstance, onBeforeUnmount } from 'vue'
import { useRouter, useRoute, onBeforeRouteLeave } from 'vue-router'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { useUmeng } from '@/utils/umeng/hook' // 导入友盟埋点

// 导入组件
import LeftMenu from '@/views/Editor/components/common/LeftMenu.vue'           // 左侧菜单组件
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'   // 顶部标题栏组件
import OperationBar from '@/views/Editor/components/common/OperationBar.vue'   // 操作栏组件
import PreviewPanel from '@/views/Editor/components/PreviewPanel.vue'          // 右侧预览面板组件
import MaterialDialog from '@/components/MaterialDialog.vue'                   // 素材对话框组件
import VideoPreviewDialog from './components/VideoPreviewDialog.vue'           // 视频预览对话框组件
import MusicDialog from '@/components/MusicDialog.vue'                         // 音乐对话框组件
import VideoLoadingDialog from './components/VideoLoadingDialog.vue'           // 视频加载对话框组件
import SubtitleDialog from '@/components/SubtitleDialog/index.vue'             // 加入空间对话框组件
import MaterialExtraction from '@/views/Editor/components/tools/MaterialExtraction.vue'             // 文案提取组件
import AlertDialog from '@/views/components/AlertDialog.vue'

// 导入Element Plus组件
import { ElDatePicker, ElSelect, ElOption, ElForm, ElFormItem, ElInput, ElInputNumber, ElSwitch, ElRadio, ElRadioGroup, ElSlider, ElButton } from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs' // 导入Element Plus中文语言包

// 导入图标和资源
import videoIcon from '@/assets/img/yinsu3.png' // 视频图标资源

// 导入API接口
import { dubbing, callbackOss } from '@/api/dubbing'                          // 配音相关API
import { getUserAlbum, searchVideos, getVideoConfig, saveVideoConfig, submitGenerateJob, getVideos, checkUserBenefits, generateVideoSnapshotUrls } from '@/api/videoEditing' // 视频编辑相关API
import { addVideoMaterial } from '@/api/material' // 导入保存视频素材接口
import { checkUploadPermission } from '@/api/upload' // 导入空间检查接口

// 导入状态管理
import { useMusicStore } from '@/stores/modules/musicStore'                   // 音乐存储
import { usePreviewStore } from '@/stores/previewStore'                        // 预览存储
import { useloginStore } from '@/stores/login'                                // 登录状态存储
import { useUserBenefits } from '@/views/modules/AIDubbing/hook/useUserInfo.js' // 用户权益管理

// 获取路由和路由器实例
const router = useRouter()
const route = useRoute()

// 初始化埋点
const umeng = useUmeng()

// 导入工具栏图标
import chengpian1 from '@/assets/img/changpian1.png'                          // 视频成片激活图标
import chengpian2 from '@/assets/img/chengpian2.png'                          // 视频成片未激活图标
import yulan1 from '@/assets/img/yulan1.png'                                  // 预览激活图标
import yulan2 from '@/assets/img/预览2.png'                                    // 预览未激活图标
import pipei1 from '@/assets/img/pipei1.png'                                  // 匹配激活图标
import pipei2 from '@/assets/img/pipei2.png'                                  // 匹配未激活图标
import cloudEditIcon from '@/assets/img/跳转云剪辑.png'                        // 云剪辑图标
import downloadIcon from '@/assets/img/btn.png'                               // 下载视频图标
import spaceIcon from '@/assets/img/加入空间.png'                               // 加入空间图标

// ===== 工具栏状态 =====
// 当前激活的工具ID：1-视频成片，2-预览，3-匹配设置
const activeTool = ref(1)
// 当前鼠标悬停的工具ID
const hoveredTool = ref(null)
// 预览面板引用
const previewPanel = ref(null)


// 获取登录状态管理
const loginStore = useloginStore()

// 自定义函数获取用户ID，当userId为null时使用默认值'11'
const getUserId = () => {
	return loginStore.userId || '11'
}
const app = document.getElementById('app');
const screenWidth = window.innerWidth;
const screenHeight = window.innerHeight;
const aspectRatio = screenHeight / screenWidth;
let rate = 953 / 1920
// ===== 匹配设置表单数据 =====
// 设置表单数据对象，包含各种视频生成参数
const settingsForm = ref({
	inputCount: 3,           // 输出数量
	width: 1920,             // 输出宽度
	height: 953 * (aspectRatio / rate),            // 输出高度
	enableEffects: false,    // 是否允许添加特效
	autoSaveToCloud: true,   // 是否自动存储至云空间
	materialVolume: 0,       // 视频素材音量
	originalVolume: 50,      // 原始素材音量
	backgroundMusic: false   // 是否添加背景配乐
})

// 保存设置：将设置参数发送到服务器
const saveSettings = async () => {
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		// 未登录，弹出登录弹窗
		proxy.$modal.open('组合式标题');
		return;
	}

	try {
		// 准备请求参数
		const params = {
			userId: getUserId(),                                           // 用户ID
			outputQuantity: String(settingsForm.value.inputCount),  // 输出数量
			width: String(settingsForm.value.width),                // 输出宽度
			height: String(settingsForm.value.height),              // 输出高度
			allowEffects: settingsForm.value.enableEffects ? "1" : "0", // 是否允许特效
			autoCloudSave: settingsForm.value.autoSaveToCloud ? "1" : "0", // 是否自动云存储
			volume: String(settingsForm.value.materialVolume),      // 素材音量
			// optimalScenes: "1",                                 // 可选参数-最佳场景
			// allowRepeatMatch: "0"                               // 可选参数-允许重复匹配
		}

		// 调用保存配置接口
		const response = await saveVideoConfig(params)

		if (response) {
			ElMessage.success('配置创建成功')
		} else {
			ElMessage.error('保存设置失败')
		}
	} catch (error) {
		console.error('保存设置失败:', error)
		ElMessage.error('保存设置失败')
	}
}

// 取消设置：重置表单数据到默认值
const cancelSettings = () => {
	// 重置表单数据到默认值
	settingsForm.value = {
		inputCount: 3,
		width: 1920,
		height: 953 * (aspectRatio / rate),
		enableEffects: false,
		autoSaveToCloud: true,
		materialVolume: 0,
		originalVolume: 50,
		backgroundMusic: false
	}
	ElMessage.info('已取消修改')
}

// 处理原始素材音量变化
const handleVolumeChange = (value) => {
	console.log('原始素材音量设置为:', value)
	// ElMessage.success(`原始素材音量已调整为 ${value.volume}%`)
}

// 处理视频素材音量变化
const handleMaterialVolumeChange = (value) => {
	console.log('视频素材音量设置为:', value)
	// ElMessage.success(`视频素材音量已调整为 ${value}%`)
}

// ===== 工具栏配置 =====
// 定义工具栏数据：包含视频成片、预览、匹配设置三个工具
const tools = [
	{
		id: 1,                // 工具ID：1-视频成片
		name: '视频成片',      // 工具名称
		img: chengpian2,      // 未激活状态图标
		activeImg: chengpian1 // 激活状态图标
	},
	{
		id: 2,               // 工具ID：2-预览
		name: '预览',         // 工具名称
		img: yulan2,         // 未激活状态图标
		activeImg: yulan1    // 激活状态图标
	},
	{
		id: 3,               // 工具ID：3-匹配设置
		name: '匹配设置',     // 工具名称
		img: pipei2,         // 未激活状态图标
		activeImg: pipei1    // 激活状态图标
	}
]

// 添加isLoadingPreview变量，用于控制预览加载状态
const isLoadingPreview = ref(false)

// 添加isLoading变量，用于控制加载状态
const isLoading = ref(false)

// 处理工具点击事件：切换当前选中的工具
const handleToolClick = async (tool) => {
	// 预览工具特殊处理：先检查登录状态
	if (tool.id === 2) {
		// 检查用户是否已登录
		if (!checkUserLogin()) {
			// 未登录，弹出登录弹窗
			proxy.$modal.open('组合式标题');
			return; // 未登录直接返回，不切换工具
		}
	}

	// 更新当前选中的工具
	activeTool.value = tool.id

	// 当点击视频成片工具时，获取最新的资源库数据
	if (tool.id === 1) {
		try {
			// ElMessage.info('正在更新资源库数据...')
			// 获取最新的资源库选项
			await fetchSourceOptions()
			// 更新视频数据
			await fetchVideoList()
		} catch (error) {
			console.error('更新资源库数据失败:', error)
			// ElMessage.error('更新资源库数据失败')
		}
	}

	// 当点击预览工具时，获取预览视频列表
	if (tool.id === 2) {
		try {
			// 设置加载状态为true
			isLoadingPreview.value = true

			// 检查是否有路由id参数
			const routeId = route.query.id;
			if (routeId) {
				console.log('使用路由id参数获取特定视频:', routeId);
				await fetchPreviewVideos(null, routeId);
			} else {
				// 没有特定id时获取所有预览视频
				await fetchPreviewVideos()
			}
		} catch (error) {
			console.error('获取预览视频失败:', error)
			// ElMessage.error('获取预览视频数据失败')
		} finally {
			// 无论请求成功或失败，最终将加载状态设为false
			isLoadingPreview.value = false
		}
	}

	// 当点击匹配设置时，获取配置参数
	if (tool.id === 3) {
		try {
			// ElMessage.info('正在获取配置参数...')
			// 获取视频配置参数
			const response = await getVideoConfig({ userId: getUserId() })

			// 如果接口返回成功，更新表单数据
			if (response) {
				settingsForm.value = {
					...settingsForm.value,
					inputCount: response.outputQuantity || 3,        // 输出数量
					width: response.width || 953 * (aspectRatio / rate),                   // 输出宽度
					height: response.height || 1920,                 // 输出高度
					enableEffects: response.allowEffects === '1',    // 是否允许特效
					autoSaveToCloud: response.autoCloudSave === '1', // 是否自动云存储
					materialVolume: Number(response.volume),         // 素材音量
				}
				console.log(settingsForm.value, 'settingsForm.value')
				// ElMessage.success('配置参数获取成功')
			}
		} catch (error) {
			console.error('获取配置参数失败:', error)
			// ElMessage.error('获取配置参数失败')
		}
	}
}

// ===== 视频剪辑相关状态 =====
// 视频片段列表
// const videoClips = ref([])

// ===== 视频对话框相关状态 =====
const showVideoDialog = ref(false)              // 控制视频对话框显示
const videoDialogTitleIcon = videoIcon          // 对话框标题图标
const materialDialogList = ref([])              // 对话框中的素材列表

// ===== 文件上传相关状态 =====
const isUploading = ref(false)                  // 是否正在上传
const uploadRequest = ref(null)                 // 上传请求引用
// 上传文件信息
const uploadFile = ref({
	name: '',                                   // 文件名
	size: 0,                                   // 文件大小
	loaded: 0,                                 // 已上传大小
	percent: 0,                                // 上传进度百分比
	type: ''                                   // 文件类型
})

// ===== 预览面板相关状态 =====
const previewTitle = ref('')                    // 预览标题
const previewContent = ref('')                  // 预览内容
const previewMusicList = ref([])                // 预览音乐列表
const previewVideoList = ref([])                // 预览视频列表

// ===== 视频素材数据 =====
// 用于指定风格模式的视频素材列表
const videoMaterials = ref([]);

// ===== 动漫素材数据 =====
const animationMaterials = ref([])              // 专辑列表数据

// ===== 筛选条件数据 =====
// 获取当前年份，用于设置默认日期范围
const currentYear = new Date().getFullYear();
const lastYear = currentYear - 1; // 上一年
// 日期范围选择器的值 - 修改为显示两年的数据
const dateRange = ref([
	`${lastYear}-01-01`,                     // 默认开始日期：上一年1月1日
	`${currentYear}-12-31`                   // 默认结束日期：当年12月31日
])
const selectedSource = ref('')                  // 选中的资源库
const sourceOptions = ref([])                   // 资源库选项列表

// ===== 匹配模式 =====
const matchMode = ref(2)                        // 修改为2-指定视频模式作为默认值（原来是1-专辑模式）

// ===== 获取用户专辑数据 =====
const fetchUserAlbums = async () => {
	try {
		// ElMessage.info('正在加载专辑数据...')

		// 准备请求参数，包括日期范围
		const params = {
			userId: getUserId(),
			startDate: dateRange.value ? dateRange.value[0] : undefined,
			endDate: dateRange.value ? dateRange.value[1] : undefined
		}

		console.log('获取专辑请求参数:', params)

		// 调用获取用户专辑API
		const response = await getUserAlbum(params)
		console.log('获取专辑响应数据:', response)

		// 处理返回的数据，映射成animationMaterials所需的格式
		if (response && Array.isArray(response) && response.length > 0) {
			// 更新专辑列表前重置选中状态
			selectedAlbumItem.value = null

			// 更新专辑列表
			animationMaterials.value = response.map((album, index) => ({
				id: album.id || index + 1,                 // 专辑ID
				tagId: album.tagId || '',                  // 标签ID
				name: album.tagName || `专辑${index + 1}`, // 专辑名称
				tagName: album.tagName || `专辑${index + 1}`, // 标签名称
				cover: album.cover || '',                  // 专辑封面
				description: album.description || '',       // 专辑描述
				count: album.videoCount || 0,              // 视频数量
				createTime: album.createTime || ''         // 创建时间
			}))

			// ElMessage.success(`已加载 ${response.length} 个专辑资源`)
		} else {
			// 清空专辑列表并重置选中状态
			animationMaterials.value = []
			selectedAlbumItem.value = null

			ElMessage.warning('未找到专辑资源')
		}
	} catch (error) {
		console.error('获取用户专辑失败:', error)
		// 出错时也需要清空列表和重置选中状态
		animationMaterials.value = []
		selectedAlbumItem.value = null

		ElMessage.error('获取专辑资源数据失败')
	}
}

// ... 更多代码注释将在后续部分添加 ...

// 修改 fetchSourceOptions 函数，添加全部选项
const fetchSourceOptions = async () => {
	try {
		console.log('开始获取下拉框选项数据')

		// 准备请求参数
		let params = {
			userId: getUserId()
		}

		// 只有在指定专辑模式下才添加日期筛选
		if (matchMode.value === 1) {
			params = {
				...params,
				startDate: dateRange.value ? dateRange.value[0] : undefined,
				endDate: dateRange.value ? dateRange.value[1] : undefined
			}
		}

		const response = await getUserAlbum(params)
		console.log('获取下拉框数据响应:', response)

		// 直接使用接口返回的数据设置选项，并添加"全部"选项
		if (response && Array.isArray(response)) {
			// 先将API返回的数据映射成选项格式
			const apiOptions = response.map(album => ({
				// 使用String(album.tagId)确保0也能被正确处理
				value: album.tagId !== undefined && album.tagId !== null ? String(album.tagId) : '',
				label: album.tagName || ''
			}));

			// 对选项进行排序，将"生成"选项提到前面
			const sortedOptions = apiOptions.sort((a, b) => {
				// 如果a的label是"我的生成"，则排在前面
				if (a.label === "我的生成") return -1;
				// 如果b的label是"我的生成"，则排在后面
				if (b.label === "我的生成") return 1;
				// 如果a的label是"生成"，则排在前面
				if (a.label === "生成") return -1;
				// 如果b的label是"生成"，则排在后面
				if (b.label === "生成") return 1;
				// 其他情况保持原顺序
				return 0;
			});

			// 调试输出，查看排序后的选项
			console.log('排序后的选项:', sortedOptions);

			// 在选项数组前添加"全部"选项
			sourceOptions.value = [
				{ value: 'all', label: '全部' },  // 添加全部选项，值设为'all'
				...sortedOptions
				// 移除这个过滤条件，因为它可能导致某些选项被过滤掉
				// .filter(option => option.value && option.label)
			]
		} else {
			sourceOptions.value = [{ value: 'all', label: '全部' }]  // 即使没有数据，也至少有全部选项
		}

		console.log('资源库选项数量:', sourceOptions.value.length, sourceOptions.value)

		// 如果当前有选择的专辑ID，确保它在下拉框中存在
		if (selectedAlbumId.value) {
			const albumId = String(selectedAlbumId.value)
			if (!sourceOptions.value.some(option => option.value === albumId)) {
				console.log('选中的专辑ID在下拉框中不存在，设置为空')
				selectedSource.value = 'all'  // 改为默认选择"全部"
			} else {
				console.log('设置下拉框选中值:', albumId)
				selectedSource.value = albumId
			}
		} else {
			selectedSource.value = 'all'  // 默认选择"全部"
		}
	} catch (error) {
		console.error('获取资源库数据失败:', error)
		// ElMessage.error('获取资源库数据失败')
		sourceOptions.value = [{ value: 'all', label: '全部' }]  // 出错时也设置全部选项
		selectedSource.value = 'all'
	}
}

// 添加在 script 部分添加一个新变量来标记是否是新会话
const isNewSession = ref(true); // 默认为新会话

// 监听路由参数变化，当URL查询参数变化时获取视频
watch(() => route.query, async (newQuery) => {
	console.log('路由参数变化:', newQuery);

	// 获取路由中的activeTool和id参数
	const routeActiveTool = newQuery.activeTool ? parseInt(newQuery.activeTool) : null;
	const routeId = newQuery.id;

	// 如果路由中指定了activeTool，设置当前工具
	if (routeActiveTool !== null) {
		console.log('从路由参数设置activeTool:', routeActiveTool);
		activeTool.value = routeActiveTool;
	}

	// 如果是预览工具且有id参数，获取特定视频
	if ((routeActiveTool === 2 || activeTool.value === 2) && routeId) {
		try {
			isLoadingPreview.value = true;
			console.log('路由参数变化，获取特定视频，id:', routeId);
			await fetchPreviewVideos(null, routeId);
		} catch (error) {
			console.error('使用路由参数获取预览视频失败:', error);
			//   ElMessage.error('获取预览视频数据失败');
		} finally {
			isLoadingPreview.value = false;
		}
	}
}, { immediate: true }); // 添加immediate:true确保组件创建时立即执行一次

// 添加在组件挂载函数中，确保无论什么模式都加载资源库数据
onMounted(async () => {
	// 检查是否是从刷新页面恢复的预览模式
	const isRefreshedPreview = localStorage.getItem('videoEditing_refreshedPreview');
	if (isRefreshedPreview && activeTool.value === 2) {
		console.log('检测到页面刷新，自动重新获取预览视频...');
		localStorage.removeItem('videoEditing_refreshedPreview');
		try {
			isLoadingPreview.value = true;
			await fetchPreviewVideos();
			console.log('页面刷新后成功获取预览视频数据');
			if (thumbnailVideos.value.length > 0) {
				console.log('选中第一个视频');
				activeVideoIndex.value = 0;
				selectVideo(0);
			}
		} catch (error) {
			console.error('页面刷新后获取预览视频失败:', error);
		} finally {
			isLoadingPreview.value = false;
		}
	}

	// 添加页面刷新事件监听
	window.addEventListener('beforeunload', handleBeforeUnload);

	// 移除路由参数处理代码，已在watch中实现

	// 从localStorage中恢复activeTool的值，如果有的话
	const savedActiveTool = localStorage.getItem('videoEditing_activeTool');
	if (savedActiveTool) {
		activeTool.value = parseInt(savedActiveTool);
		console.log('从localStorage恢复activeTool值:', activeTool.value);

		// 恢复后立即清除，避免影响下次正常访问
		localStorage.removeItem('videoEditing_activeTool');

		// 如果恢复的是预览工具(activeTool=2)，则自动获取预览视频
		if (activeTool.value === 2 && !route.query.id) { // 只有没有路由id参数时才执行
			try {
				isLoadingPreview.value = true;
				// 获取预览视频并添加回调确保选择第一个视频
				fetchPreviewVideos().then(() => {
					// 确保在视频加载完成后选中第一个视频
					if (thumbnailVideos.value.length > 0) {
						console.log('预览模式：自动选中第一个视频');
						activeVideoIndex.value = 0;
						selectVideo(0);
					}
				}).catch(error => {
					console.error('预览视频加载失败:', error);
				});
			} catch (error) {
				console.error('获取预览视频失败:', error);
				// ElMessage.error('获取预览视频数据失败');
			} finally {
				isLoadingPreview.value = false;
			}
		}
	}
	// 如果已经是预览工具状态但没有从localStorage恢复，也需要获取预览视频
	else if (activeTool.value === 2 && !route.query.id) { // 只有没有路由id参数时才执行
		console.log('当前已是预览模式，获取预览视频');
		try {
			isLoadingPreview.value = true;
			fetchPreviewVideos().then(() => {
				// 确保在视频加载完成后选中第一个视频，特别是在智能匹配后刷新的情况
				if (thumbnailVideos.value.length > 0) {
					console.log('预览视频加载完成，选中第一个视频');
					activeVideoIndex.value = 0;
					selectVideo(0);
				}
			}).catch(error => {
				console.error('预览视频加载失败:', error);
			});
		} catch (error) {
			console.error('获取预览视频失败:', error);
			// ElMessage.error('获取预览视频数据失败');
		} finally {
			isLoadingPreview.value = false;
		}
	}

	// 检查是否是智能匹配后的刷新
	const autoSelectVideo = localStorage.getItem('autoSelectFirstVideo');

	// 如果是，则清除这个标志
	if (autoSelectVideo) {
		localStorage.removeItem('autoSelectFirstVideo');
		console.log('检测到智能匹配后刷新，将自动选中第一个视频');
	}

	// 根据默认模式加载相应数据
	if (matchMode.value === 1) {
		// 如果默认是专辑模式，加载专辑数据
		fetchUserAlbums();
	} else {
		// 如果默认是视频模式，先加载资源库选项，再加载视频数据
		await fetchSourceOptions();
		// 调用fetchVideoList获取视频数据
		const videoResponse = await fetchVideoList();
		// 如果有返回数据且需要额外处理，可以在这里传给initVideoMaterials
		if (videoResponse && Array.isArray(videoResponse)) {
			// 可以在这里对数据进行额外处理，例如初始化颜色等
			initVideoMaterials(videoResponse);
		}
	}

	// 确保初始状态下没有选中的视频
	isAllSelected.value = false;

	// 添加延时恢复，确保在DOM更新后执行
	setTimeout(() => {
		// 检查是否需要再次恢复选中状态（以防前面的恢复不成功）
		if (previewStore.selectedVideoIds && previewStore.selectedVideoIds.length > 0 &&
			selectedCount.value === 0) {
			console.log('延时恢复视频选中状态');
			restoreVideoSelectionState();
		}
	}, 500);

	// 添加延时调用确保没有默认全选
	setTimeout(ensureNoDefaultSelection, 200);
});

// 添加组件卸载钩子
onBeforeUnmount(() => {
	// 移除页面刷新事件监听
	window.removeEventListener('beforeunload', handleBeforeUnload);
});

// 处理页面刷新前事件
const handleBeforeUnload = () => {
	// 如果当前是预览模式(activeTool=2)，设置标记以便刷新后恢复
	if (activeTool.value === 2) {
		console.log('页面刷新前保存预览模式状态');
		localStorage.setItem('videoEditing_refreshedPreview', 'true');
		localStorage.setItem('videoEditing_activeTool', '2');
	}
};

// 添加一个变量标记是否是用户操作
const isUserAction = ref(false);

// 在用户手动添加内容的函数中设置标记
const handleAddRole = () => {
	isUserAction.value = true; // 标记这是用户操作
	ElMessage.info('添加配音角色');
	// 跳转到配音页面，修改路径为 VoiceOver
	router.push('/VoiceOver');
	// 操作结束后重置标记
	setTimeout(() => { isUserAction.value = false; }, 100);
}

const handleAddMusic = (openDialog = false) => {
	isUserAction.value = true; // 添加这行
	// 打开弹窗前，先从musicStore同步数据到previewMusicList
	if (musicStore.musicList && musicStore.musicList.length > 0) {
		// 使用深拷贝确保数据独立
		previewMusicList.value = JSON.parse(JSON.stringify(musicStore.musicList))
	}

	// 打开音乐对话框
	musicDialogVisible.value = true;
	// 操作结束后重置标记
	setTimeout(() => { isUserAction.value = false; }, 100); // 添加这行
}

const handleAddVideo = (openDialog = false) => {
	isUserAction.value = true; // 添加这行

	// 确保materialDialogList已包含所有选中的视频
	updateMaterialDialogList()

	if (openDialog) {
		showVideoDialog.value = true
	} else {
		ElMessage.info('添加视频')
		showVideoDialog.value = true
	}

	setTimeout(() => { isUserAction.value = false }, 100)
}

// 改进updateMaterialDialogList函数，完善重复检查逻辑
const updateMaterialDialogList = () => {
	// 清空现有列表
	materialDialogList.value = [];

	// 临时存储已添加的URL，用于去重
	const addedUrls = new Set();

	// 先添加来自预览视频列表的视频
	if (previewVideoList.value && previewVideoList.value.length > 0) {
		previewVideoList.value.forEach((video, index) => {
			// 只有当URL不为空且未添加过时才添加
			const videoUrl = video.url || '';
			if (videoUrl && !addedUrls.has(videoUrl)) {
				materialDialogList.value.push({
					id: `preview-video-${index}`,
					name: video.name || `视频${index + 1}`,
					url: videoUrl,
					thumbnailUrl: video.thumbnailUrl || '',
					duration: video.duration || '00:00',
					isPlaying: false
				});
				// 记录已添加的URL
				addedUrls.add(videoUrl);
			}
		});
	}

	// 然后添加选中的视频素材
	videoMaterials.value.forEach((video, index) => {
		if (video.selected) {
			// 获取视频URL
			const videoUrl = video.url || video.videoUrl || '';

			// 检查该视频URL是否已存在
			if (videoUrl && !addedUrls.has(videoUrl)) {
				materialDialogList.value.push({
					id: video.id || `video-${index}`,
					name: video.title || `视频${index + 1}`,
					url: videoUrl,
					thumbnailUrl: video.thumbnailUrl || '',
					duration: video.duration || '00:00',
					isPlaying: false
				});
				// 记录已添加的URL
				addedUrls.add(videoUrl);
			}
		}
	});

	console.log(`视频弹窗列表更新完成，共 ${materialDialogList.value.length} 个视频`);
}



// 修改 setAlbumMode 函数，使用标记避免重复调用
const isUpdatingDateRange = ref(false) // 添加标记变量

// 改进setAlbumMode函数，使每次点击都调用API
const setAlbumMode = () => {
	// 如果已经是专辑模式，避免不必要的操作
	if (matchMode.value === 1) {
		console.log('已经是专辑模式，无需切换')
		return
	}

	// 设置标记，避免触发 watch 监听器中的 API 调用
	isUpdatingDateRange.value = true

	// 更新模式
	matchMode.value = 1

	// 重置日期范围为两年
	const currentYear = new Date().getFullYear()
	const lastYear = currentYear - 1
	dateRange.value = [
		`${lastYear}-01-01`,
		`${currentYear}-12-31`
	]

	// 使用nextTick确保DOM更新后再执行
	nextTick(() => {
		// 移除条件判断，确保每次都获取最新数据
		fetchUserAlbums()

		// 重置标记
		isUpdatingDateRange.value = false
	})
}

// 改进setStyleMode函数，防止重复API调用
const setStyleMode = () => {
	// 如果已经是视频模式，避免不必要的操作
	if (matchMode.value === 2) {
		console.log('已经是视频模式，无需切换')
		return
	}

	// 设置标记，避免触发 watch 监听器中的 API 调用
	isUpdatingDateRange.value = true

	// 更新模式
	matchMode.value = 2

	// 重置日期范围
	const currentYear = new Date().getFullYear()
	dateRange.value = [
		`${currentYear}-01-01`,
		`${currentYear}-12-31`
	]


	// 使用nextTick确保DOM更新后再执行
	nextTick(async () => {
		// 重置selectedSource为all，确保切换到指定视频模式时去掉tagId参数
		selectedSource.value = 'all'
		selectedAlbumId.value = ''
		console.log('切换模式前 - 专辑ID:', selectedAlbumId.value, '资源下拉框:', selectedSource.value)

		// 确保下拉框有选项数据，在切换模式时主动获取
		await fetchSourceOptions()


		// 添加这一行：明确调用fetchVideoList获取视频数据
		await fetchVideoList()

		// 新增：处理视频时长，确保视频时长显示正确
		if (videoMaterials.value && videoMaterials.value.length > 0) {
			console.log('开始处理视频时长...');
			await processVideoWithDelay(videoMaterials.value);
			console.log('视频时长处理完成');
		}

		// 重置标记
		isUpdatingDateRange.value = false

		// window.location.reload() // 不再需要刷新页面
	})
}



// 添加新的状态变量来存储选中专辑的视频列表
const selectedAlbumVideos = ref([])

// 修改选择素材函数，添加获取专辑视频的功能
const selectMaterial = async (material) => {
	// 只保留选择专辑的基本功能，不再跳转到指定视频
	// ElMessage.success(`已选择专辑: ${material.name}`)

	// 记录选择的专辑ID
	// 确保tagId为0时也能正确处理
	const albumId = material.tagId !== undefined && material.tagId !== null ? String(material.tagId) : ''
	selectedAlbumId.value = albumId

	// 设置选中的专辑项 - 存储完整的专辑对象，而不仅仅是ID
	selectedAlbumItem.value = {
		...material,
		albumId: albumId, // 确保有albumId属性
		thumbnailUrl: material.thumbnailUrl || material.coverUrl || ''
	}

	console.log('选择专辑:', material.name, 'tagID:', selectedAlbumId.value, '专辑对象:', selectedAlbumItem.value)

	// 新增：获取该专辑下的视频列表
	// 注意：当tagId为0时，albumId为'0'字符串，也是有效值
	if (albumId !== '') {
		try {
			// ElMessage.info('正在获取专辑视频数据...')

			// 准备请求参数
			const params = {
				userId: getUserId(),
				tagId: albumId,
				startDate: dateRange.value ? dateRange.value[0] : undefined,
				endDate: dateRange.value ? dateRange.value[1] : undefined
			}

			console.log('获取专辑视频请求参数:', params)

			// 调用API获取视频列表
			const response = await searchVideos(params)

			// 处理返回的数据
			if (response && Array.isArray(response) && response.length > 0) {
				// 保存专辑视频列表
				selectedAlbumVideos.value = response.map((video, index) => ({
					id: video.id || `v-${index}`,
					title: video.title || `视频${index + 1}`,
					duration: video.duration || '00:00',
					thumbnailUrl: video.thumbnailUrl || '',
					url: video.url || '',
					selected: true  // 默认全部选中
				}))

				// ElMessage.success(`已获取专辑"${material.name}"下的 ${response.length} 个视频`)
				console.log('专辑视频数据:', selectedAlbumVideos.value)

				// 新增: 加载视频真实时长
				// ElMessage.info('正在解析视频时长，请稍候...')

				// 使用防抖函数处理视频
				await processVideoWithDelay(selectedAlbumVideos.value);

				// ElMessage.success(`已获取专辑"${material.name}"下的 ${response.length} 个视频并解析时长`)
			} else {
				// 清空专辑视频列表
				selectedAlbumVideos.value = []
				ElMessage.warning(`专辑"${material.name}"下没有视频数据`)
			}
		} catch (error) {
			console.error('获取专辑视频失败:', error)
			selectedAlbumVideos.value = []
			ElMessage.error('获取专辑视频数据失败')
		}
	}
}

// 全选相关状态
const isAllSelected = ref(false)

// 计算选中的视频数量
const selectedCount = computed(() => {
	return videoMaterials.value.filter(video => video.selected).length
})



// 监听选中数量变化，自动更新全选状态
watch(selectedCount, (newCount) => {
	if (newCount === 0) {
		isAllSelected.value = false
	} else if (newCount === videoMaterials.value.length) {
		isAllSelected.value = true
	}
})

// 只保留这一个完整的监听器，它包含了防止重复调用的逻辑
watch(dateRange, () => {
	// 只有在不是由 setAlbumMode/setStyleMode 引起的变化时才调用 API
	if (isUpdatingDateRange.value) {
		console.log('日期范围变化由模式切换引起，跳过 API 调用')
		return
	}

	if (matchMode.value === 1) {
		fetchUserAlbums()
	} else if (matchMode.value === 2) {
		fetchVideoList()
	}
})

// 视频选择方法 - 保持允许手动选择
const toggleVideoSelection = (video, index) => {
	// 标记为内部更新，避免触发自动弹窗
	isUserAction.value = false;

	// 切换选中状态
	video.selected = !video.selected;

	// 获取所有选中的视频
	const selectedVideos = videoMaterials.value.filter(v => v.selected);

	if (selectedVideos.length > 0) {
		// 如果有选中的视频，更新预览区并包含 ID
		previewVideoList.value = selectedVideos.map(v => ({
			name: v.title || '视频素材',
			url: v.url || v.videoUrl || '',
			thumbnailUrl: v.thumbnailUrl || '',
			id: v.videoId || v.id || `v-${index}` // 保存 videoId 或 id
		}));

		// 重要：将选中状态保存到Pinia，确保路由跳转后能恢复
		// 1. 保存包含ID的视频列表
		previewStore.setVideoList(JSON.parse(JSON.stringify(previewVideoList.value)));

		// 2. 保存选中的视频ID，用于后续恢复
		const selectedIds = selectedVideos.map(v => v.videoId || v.id || `v-${index}`);
		previewStore.setSelectedVideoIds(selectedIds);

		console.log(`已保存${selectedVideos.length}个选中视频的ID到Pinia:`, selectedIds);
	} else {
		// 如果没有选中的视频，清空预览区
		previewVideoList.value = [];
		previewStore.setVideoList([]);
		previewStore.setSelectedVideoIds([]);
	}

	// 更新选中数量
	updateSelectedCount();

	// 恢复标记
	isUserAction.value = true;
};

// 更新时确保没有设定默认选中
const updateSelectedCount = () => {
	selectedCount.value = videoMaterials.value.filter(video => video.selected).length;
};

// 处理操作栏动作
const handleBarAction = (action) => {
	console.log('操作栏动作:', action)
	switch (action) {
		case 'new':
			ElMessage.info('新建视频项目')
			// 重置项目状态
			previewVideoList.value = []
			break
		case 'save':
			ElMessage.info('查看最近视频项目')
			break
		case 'edit':
			ElMessage.info('进入剪辑模式')
			break
		case 'export':
			// 使用与downloadVideo相同的检查逻辑
			if (!videoPlayer.value || !videoPlayer.value.src) {
				ElMessage.warning('没有可导出的视频')
				return
			}
			// 调用下载视频函数
			downloadVideo()
			break
	}
}


// 处理生成视频
const handleGenerateVideo = () => {
	ElMessage.info('生成视频')
}

// 视频对话框相关方法
const closeVideoDialog = () => {
	showVideoDialog.value = false
}

// 改进视频对话框确认处理
const handleVideoDialogConfirm = (selectedItems) => {
	if (selectedItems && selectedItems.length > 0) {
		// 确保每个选择的项目都有缩略图
		selectedItems.forEach(item => {
			if (item.url && !item.thumbnailUrl) {
				// 尝试生成缩略图
				const tempList = [item];
				extractVideoThumbnails(tempList);
			}
		});

		// 获取选中的第一个视频
		const selectedItem = selectedItems[0];

		// 更新预览视频列表
		previewVideoList.value = [{
			name: selectedItem.name || '未命名视频',
			url: selectedItem.url || '',
			thumbnailUrl: selectedItem.thumbnailUrl || ''
		}];

		// ElMessage.success(`已选择视频: ${selectedItem.name || '未命名视频'}`);
	}

	// 关闭对话框
	showVideoDialog.value = false;
}

// 完全重写视频对话框移除函数，确保三处同步更新
const handleVideoDialogRemove = (item, updateSelectAll = false) => {
	console.log('从弹窗中移除视频:', item);

	// 1. 从素材对话框列表中移除
	const dialogIndex = materialDialogList.value.findIndex(i => i.id === item.id);
	if (dialogIndex >= 0) {
		materialDialogList.value.splice(dialogIndex, 1);
	}

	// 2. 同时更新右侧预览区视频列表
	// 检查该视频是否在预览列表中 (根据URL匹配)
	const videoUrl = item.url || '';
	const previewIndex = previewVideoList.value.findIndex(v => v.url === videoUrl);
	if (previewIndex >= 0) {
		// 创建新数组以避免直接修改
		const newPreviewList = [...previewVideoList.value];
		newPreviewList.splice(previewIndex, 1);
		previewVideoList.value = newPreviewList;
		console.log('已从预览区移除视频');
	}

	// 3. 同时在视频材料列表中取消选中对应的视频
	// 尝试通过ID匹配
	let videoIndex = videoMaterials.value.findIndex(video =>
		video.id === item.id || `video-${videoMaterials.value.indexOf(video)}` === item.id
	);

	// 如果ID匹配失败，尝试通过URL匹配
	if (videoIndex < 0 && videoUrl) {
		videoIndex = videoMaterials.value.findIndex(video =>
			video.url === videoUrl || video.videoUrl === videoUrl
		);
	}

	if (videoIndex >= 0) {
		// 取消选中状态
		videoMaterials.value[videoIndex].selected = false;
		console.log(`已取消选中视频: ${videoMaterials.value[videoIndex].title}`);

		// 更新选中计数
		updateSelectedCount();

		// 如果需要更新全选状态
		if (updateSelectAll || isAllSelected.value) {
			isAllSelected.value = false;
			console.log('已取消全选状态');
		}
	}

	// 提示用户
	// ElMessage.success('已移除视频');
}

// 改进视频对话框播放切换处理
const handleVideoDialogTogglePlay = (index) => {
	console.log(`切换播放视频 ${index}`)

	// 确保materialDialogList中的视频项有正确的URL
	if (materialDialogList.value[index] && !materialDialogList.value[index].url) {
		ElMessage.warning('该视频没有可播放的URL')
	}
}

// 处理视频对话框上传
const handleVideoDialogUpload = async (formData, callbacks) => {
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		// 未登录，弹出登录弹窗
		proxy.$modal.open('组合式标题');
		// 回调错误
		if (callbacks && callbacks.onError) {
			callbacks.onError(new Error('请先登录'));
		}
		return;
	}

	// 从formData中获取文件
	const file = formData.get('file')
	if (!file) {
		console.error('没有接收到上传文件')
		if (callbacks && callbacks.onError) {
			callbacks.onError(new Error('没有接收到上传文件'))
		}
		return
	}

	console.log('开始上传视频:', file.name)

	try {
		// 检查文件类型
		const allowedTypes = ['video/'];
		if (!allowedTypes.some(type => file.type.startsWith(type)) &&
			!file.name.toLowerCase().endsWith('.mp4')) {
			ElMessage.error('请上传视频文件');
			if (callbacks && callbacks.onError) {
				callbacks.onError(new Error('请上传视频文件'));
			}
			return;
		}

		// 检查文件大小（这里设置最大500MB）
		const maxSize = 500 * 1024 * 1024; // 500MB
		if (file.size > maxSize) {
			ElMessage.error('文件大小不能超过500MB');
			if (callbacks && callbacks.onError) {
				callbacks.onError(new Error('文件大小不能超过500MB'));
			}
			return;
		}

		// 获取文件扩展名确定文件类型
		const fileExtension = file.name.split('.').pop().toLowerCase();
		const fileType = 'mp4';

		// 调用 dubbing API 获取 OSS 上传凭证
		const response = await dubbing({ userId: getUserId(), fileType });

		// 准备OSS上传所需的参数
		const newFormData = new FormData();
		newFormData.append('OSSAccessKeyId', response.accessKeyId);
		newFormData.append('policy', response.policy);
		newFormData.append('signature', response.signature);
		newFormData.append('key', `${response.key.replace(/[^\/]+$/, '')}${file.name}`);
		newFormData.append('file', file);

		// 使用 XHR 上传文件，以便能够跟踪进度
		const xhr = new XMLHttpRequest();

		// 设置进度监听
		xhr.upload.onprogress = (e) => {
			if (e.lengthComputable) {
				const percent = Math.round(e.loaded / e.total * 100);
				// 通知进度回调
				if (callbacks && callbacks.onProgress) {
					callbacks.onProgress(percent);
				}
			}
		};

		// 上传完成后的处理
		xhr.onload = async () => {
			try {
				if (xhr.status >= 200 && xhr.status < 300) {
					// 判断文件类型
					const materialType = 'video';
					const userId = getUserId();
					const fileNameWithoutExt = file.name.split('.').slice(0, -1).join('.');

					// 构建临时URL以获取时长
					const ossFileUrl = `${response.host}/${response.key.replace(/[^\/]+$/, '')}${file.name}`;
					console.log('尝试从临时URL获取视频时长:', ossFileUrl);

					// 默认时长为0
					let durationInSeconds = 0;
					let formattedDuration = '0:00';

					// 尝试获取视频时长
					try {
						const durationInfo = await loadVideoAndGetDuration(ossFileUrl);
						durationInSeconds = durationInfo.durationInSeconds;
						formattedDuration = durationInfo.formattedDuration;
						console.log('成功获取视频时长:', durationInSeconds, '秒,', formattedDuration);
					} catch (durationError) {
						console.error('获取视频时长失败，使用默认值0:', durationError);
					}

					// 调用 callbackOss 接口并传入视频时长
					const callbackResponse = await callbackOss({
						userId: userId,
						materialName: fileNameWithoutExt,
						ossPath: response.key.replace(/[^\/]+$/, '') + file.name,
						fileSize: String(file.size),
						fileExtension: fileExtension,
						tagNames: '1',
						materialType: materialType,
						isPrivate: '1',
						storage_path: `/material/${userId}/${file.name}`,
						duration: durationInSeconds // 使用获取到的秒数
					});

					// 添加时长信息到callbackResponse
					callbackResponse.duration = formattedDuration;
					callbackResponse.durationInSeconds = durationInSeconds;

					// 成功后调用回调并传递包含时长的响应
					if (callbacks && callbacks.onSuccess) {
						callbacks.onSuccess(callbackResponse);
					}

					// 上传成功后重新获取视频列表，但不自动选中
					await fetchVideoList();

					// 显示上传成功弹窗
					ElMessage.success(`视频 "${fileNameWithoutExt}" 上传成功，时长: ${formattedDuration}`);

				} else {
					throw new Error(xhr.statusText || '上传失败');
				}
			} catch (error) {
				console.error('处理错误:', error);
				if (callbacks && callbacks.onError) {
					callbacks.onError(error);
				}
			}
		};

		// 错误处理
		xhr.onerror = (error) => {
			console.error('上传错误:', error);
			if (callbacks && callbacks.onError) {
				callbacks.onError(error || new Error('文件上传失败'));
			}
		};

		// 发送请求
		xhr.open('POST', response.host, true);
		xhr.send(newFormData);

	} catch (error) {
		console.error('处理错误:', error);
		if (callbacks && callbacks.onError) {
			callbacks.onError(error);
		}
	}
}



// 文件上传处理函数
const handleFileChange = async (eventOrFile) => {
	try {
		// 检查用户是否已登录
		if (!checkUserLogin()) {
			// 未登录，弹出登录弹窗
			proxy.$modal.open('组合式标题');
			return;
		}

		// 确保我们获取到正确的file对象，无论传入的是DOM事件还是文件对象
		let event, file;

		if (eventOrFile instanceof Event) {
			event = eventOrFile;
			file = event.target.files[0];
		} else if (eventOrFile && eventOrFile.target && eventOrFile.target.files) {
			event = eventOrFile;
			file = event.target.files[0];
		} else if (eventOrFile && eventOrFile.file) {
			// 处理MaterialDialog组件传递的文件
			file = eventOrFile.file;
		} else {
			console.error('无效的文件参数');
			return;
		}

		if (!file) return;

		// 检查文件类型
		const allowedTypes = ['video/'];
		if (!allowedTypes.some(type => file.type.startsWith(type)) &&
			!file.name.toLowerCase().endsWith('.mp4')) {
			ElMessage.error('请上传视频文件');
			return;
		}

		// 检查文件大小（这里设置最大1024MB）
		const maxSize = 1024 * 1024 * 1024; // 1GB
		if (file.size > maxSize) {
			ElMessage.error('文件大小不能超过1GB');
			return;
		}

		// 显示全局loading
		const loadingInstance = ElLoading.service({
			lock: true,
			text: '权限检查中...',
			background: 'rgba(0, 0, 0, 0.7)'
		});

		// 空间权限检查
		const fileSizeMB = Math.ceil(file.size / 1024 / 1024); // 转换为MB并向上取整
		const userId = getUserId();
		const response1 = await checkUploadPermission({
			userId: userId,
			feat: "空间",
			need: fileSizeMB
		});
		console.log(response1, 'response1');

		// 关闭loading
		loadingInstance.close();

		// 检查返回结果
		if (response1 && response1.content && response1.content.result === false) {
			// 显示空间不足对话框
			insufficientSpaceDialogVisible.value = true;
			return;
		}
		isUploading.value = true;

		// 设置上传文件信息
		uploadFile.value = {
			name: file.name,
			size: file.size,
			loaded: 0,
			percent: 0,
			type: file.type
		};

		// 获取文件扩展名确定文件类型
		const fileExtension = file.name.split('.').pop().toLowerCase();
		const fileType = 'mp4';

		// 调用 dubbing API 获取 OSS 上传凭证
		const response = await dubbing({ userId: getUserId(), fileType });

		// 去掉文件名的后缀
		const fileNameWithoutExt = file.name.split('.').slice(0, -1).join('.');

		const newFormData = new FormData();
		// 添加 OSS 需要的参数
		newFormData.append('OSSAccessKeyId', response.accessKeyId);
		newFormData.append('policy', response.policy);
		newFormData.append('signature', response.signature);
		newFormData.append('key', `${response.key.replace(/[^\/]+$/, '')}${file.name}`);
		newFormData.append('file', file);

		// 使用 XHR 上传文件，以便能够跟踪进度
		const xhr = new XMLHttpRequest();

		// 保存上传请求引用，以便能够取消
		uploadRequest.value = xhr;

		// 设置进度监听
		xhr.upload.onprogress = (e) => {
			if (e.lengthComputable) {
				uploadFile.value.percent = Math.round(e.loaded / e.total * 100);
				uploadFile.value.loaded = e.loaded;
			}
		};

		// 上传完成后的处理
		xhr.onload = async () => {
			try {
				if (xhr.status >= 200 && xhr.status < 300) {
					// 判断文件类型
					const materialType = 'video';
					const userId = getUserId();

					// 构建临时URL以获取时长
					const ossFileUrl = `${response.host}/${response.key.replace(/[^\/]+$/, '')}${file.name}`;
					console.log('尝试从临时URL获取视频时长:', ossFileUrl);

					// 默认时长为0
					let durationInSeconds = 0;
					let formattedDuration = '0:00';

					// 尝试获取视频时长
					try {
						const durationInfo = await loadVideoAndGetDuration(ossFileUrl);
						durationInSeconds = durationInfo.durationInSeconds;
						formattedDuration = durationInfo.formattedDuration;
						console.log('成功获取视频时长:', durationInSeconds, '秒,', formattedDuration);
					} catch (durationError) {
						console.error('获取视频时长失败，使用默认值0:', durationError);
					}

					// 调用 callbackOss 接口并传入视频时长
					const callbackResponse = await callbackOss({
						userId: userId,
						materialName: fileNameWithoutExt,
						ossPath: response.key.replace(/[^\/]+$/, '') + file.name,
						fileSize: String(file.size),
						fileExtension: fileExtension,
						tagNames: '1',
						materialType: materialType,
						isPrivate: '1',
						storage_path: `/material/${userId}/${file.name}`,
						duration: durationInSeconds // 使用获取到的秒数
					});

					// 重置上传状态
					uploadFile.value = {
						...uploadFile.value,
						percent: 100,
						loaded: file.size
					};

					// 显示上传成功弹窗
					ElMessage.success(`视频 "${fileNameWithoutExt}" 上传成功，时长: ${formattedDuration}`);

					// 上传成功后重新获取视频列表，但不自动添加到materialDialogList
					await fetchVideoList();

					// 设置短暂延时后重置上传状态
					setTimeout(() => {
						isUploading.value = false;
						uploadRequest.value = null;
					}, 1000);
				} else {
					throw new Error(xhr.statusText || '上传失败');
				}
			} catch (error) {
				console.error('处理错误:', error);
				ElMessage.error(error.message || '文件处理失败');
				isUploading.value = false;
				uploadRequest.value = null;
			}
		};

		// 错误处理
		xhr.onerror = (error) => {
			console.error('上传错误:', error);
			ElMessage.error('文件上传失败');
			// 发生错误时重置所有状态
			isUploading.value = false;
			uploadFile.value.percent = 0;
			uploadRequest.value = null;
		};

		// 发送请求
		xhr.open('POST', response.host, true);
		xhr.send(newFormData);

		// 重置 input，使得相同文件能够重复选择
		if (event && event.target) {
			event.target.value = '';
		}

	} catch (error) {
		console.error('处理错误:', error);
		ElMessage.error('上传准备失败: ' + error.message);
		// catch 块中也要重置所有状态
		isUploading.value = false;
		uploadFile.value = {
			name: '',
			size: 0,
			loaded: 0,
			percent: 0,
			type: ''
		};
		uploadRequest.value = null;
	}
};

// 取消上传方法
const cancelUpload = () => {
	// 重置上传文件状态
	uploadFile.value = {
		name: '',
		size: 0,
		loaded: 0,
		percent: 0,
		type: ''
	};

	// 如果有正在进行的上传请求，取消它
	if (uploadRequest.value) {
		uploadRequest.value.abort();
		uploadRequest.value = null;
	}

	// 重置其他相关状态
	isUploading.value = false;

	ElMessage.info('已取消上传');
};


// 视频播放相关的变量和方法
const videoPlayer = ref(null) // 保留视频引用
const thumbnailsListRef = ref(null) // 缩略图列表引用
const activeVideoIndex = ref(0) // 当前选中的视频索引

// 添加滑动按钮显示状态变量
const isLeftScrollVisible = ref(false);
const isRightScrollVisible = ref(true);

// 缩略图视频数据
const thumbnailVideos = ref([]);
// 空数组代替原来的假数据

// 选择视频
const selectVideo = (index) => {
	if (index < 0 || index >= thumbnailVideos.value.length) {
		console.error('无效的视频索引:', index)
		return
	}

	activeVideoIndex.value = index
	const selectedVideo = thumbnailVideos.value[index]
	console.log('选择视频:', selectedVideo)

	// 如果视频元素存在，则设置视频源但不自动播放
	if (videoPlayer.value) {
		// 优先使用ossPath字段作为视频源，其次使用url
		const videoSource = selectedVideo.ossPath || selectedVideo.url || selectedVideo.videoUrl
		if (videoSource) {
			console.log('设置视频源:', videoSource)
			videoPlayer.value.src = videoSource
			videoPlayer.value.load()
			// 移除自动播放代码，使用户需要手动点击播放
			console.log(`已加载视频: ${selectedVideo.title}，等待用户播放`)

			// 同时更新previewVideoList和Pinia store - 确保数据同步
			// 但保留左侧视频的选中状态
			const videoItem = {
				name: selectedVideo.title || '视频素材',
				url: videoSource,
				thumbnailUrl: selectedVideo.thumbnailUrl || '',
				id: selectedVideo.id || selectedVideo.videoId || `video-${Date.now()}`
			};

			// 更新预览视频列表 - 但不影响左侧选中状态
			// 检查预览列表中是否已包含此视频，避免重复添加
			const existingIndex = previewVideoList.value.findIndex(v =>
				v.url === videoSource || v.id === videoItem.id
			);

			if (existingIndex === -1) {
				// 如果不存在，则添加到预览列表，而不是替换整个列表
				previewVideoList.value = [...previewVideoList.value, videoItem];
			}

			// 更新Pinia store - 但保持左侧选中状态不变
			if (previewStore) {
				// 获取当前列表并添加新项，而不是替换
				const currentList = [...(previewStore.videoList || [])];
				const storeExistingIndex = currentList.findIndex(v =>
					v.url === videoSource || v.id === videoItem.id
				);

				if (storeExistingIndex === -1) {
					currentList.push(videoItem);
					previewStore.setVideoList(currentList);
				}

				// 更新当前视频URL
				previewStore.setCurrentVideoUrl(videoSource);
			}
		} else {
			console.log(`选择了视频: ${selectedVideo.title}，但没有可用的视频URL`)
			ElMessage.warning('该视频暂无可播放地址')
		}
	} else {
		console.error('视频播放器元素不存在')
	}
}

// 监听滚动事件，更新滑动按钮的显示状态
const updateScrollButtonsVisibility = () => {
	if (!thumbnailsListRef.value) return;

	const { scrollLeft, scrollWidth, clientWidth } = thumbnailsListRef.value;

	// 左侧按钮显示条件：已经滚动了一些距离
	isLeftScrollVisible.value = scrollLeft > 0;

	// 右侧按钮显示条件：还有内容可以向右滚动
	isRightScrollVisible.value = scrollLeft + clientWidth < scrollWidth - 5; // 5px容差
};

// 缩略图滚动控制方法
const scrollThumbnails = (direction) => {
	if (!thumbnailsListRef.value) return;

	const scrollContainer = thumbnailsListRef.value;
	const scrollAmount = 240; // 每次滚动的像素数，可以根据需要调整

	if (direction === 'right') {
		scrollContainer.scrollBy({ left: scrollAmount, behavior: 'smooth' });
	} else if (direction === 'left') {
		scrollContainer.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
	}

	// 滚动后更新按钮显示状态（需要延时等待滚动完成）
	setTimeout(updateScrollButtonsVisibility, 300);
};

// 在script部分添加一个新的函数，用于从视频URL获取第一帧作为缩略图
const extractVideoThumbnails = (videoList) => {
	videoList.forEach((video, index) => {
		// 如果已经有url属性但没有thumbnailUrl属性，尝试提取第一帧
		if (video.url && !video.thumbnailUrl) {
			const videoElement = document.createElement('video');
			videoElement.crossOrigin = 'anonymous'; // 处理跨域问题
			videoElement.src = video.url;

			// 监听视频加载完成事件
			videoElement.addEventListener('loadeddata', () => {
				// 确保视频数据已加载
				videoElement.currentTime = 0; // 设置为第一帧
			});

			// 监听视频时间更新，表明已经到达指定时间点
			videoElement.addEventListener('timeupdate', function onTimeUpdate() {
				// 只需要处理一次
				videoElement.removeEventListener('timeupdate', onTimeUpdate);

				// 使用canvas绘制当前视频帧
				const canvas = document.createElement('canvas');
				canvas.width = videoElement.videoWidth || 160;
				canvas.height = videoElement.videoHeight || 90;
				const ctx = canvas.getContext('2d');
				ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

				try {
					// 将canvas内容转换为data URL
					const thumbnailUrl = canvas.toDataURL('image/jpeg');
					// 更新视频对象的缩略图URL
					if (videoList[index]) {
						videoList[index].thumbnailUrl = thumbnailUrl;
					}
				} catch (e) {
					console.error('无法生成缩略图:', e);
				}
			});

			// 加载视频
			videoElement.load();
		}
	});
};

// 添加被选中的专辑ID
const selectedAlbumId = ref('')



// 修改 fetchVideoList 函数，移除获取视频时长的部分
const fetchVideoList = async () => {
	try {
		// 优化条件判断，避免重复调用
		// 只有当sourceOptions完全为空时才获取选项
		if (sourceOptions.value.length === 0) {
			console.log('资源库选项为空，获取选项数据')
			await fetchSourceOptions()
		} else {
			console.log('资源库选项已存在，数量:', sourceOptions.value.length)
		}

		// 移除加载提示信息
		// ElMessage.info('正在加载视频数据...')

		// 准备请求参数
		const params = {
			userId: getUserId(),
			startDate: dateRange.value ? dateRange.value[0] : undefined,
			endDate: dateRange.value ? dateRange.value[1] : undefined
		}

		// 确保资源库选项存在后，再添加专辑ID参数
		// 特别处理tagId为0的情况，确保它能被正确传递到API
		if (selectedSource.value && selectedSource.value !== 'all') {
			params.tagId = selectedSource.value
			console.log('添加专辑筛选，tagId:', params.tagId)
		}

		console.log('搜索视频请求参数:', params)

		// 调用搜索视频API
		const response = await searchVideos(params)

		// 处理返回的数据
		if (response && Array.isArray(response) && response.length > 0) {
			// 直接使用API返回的时长，通过formatVideoDuration格式化
			videoMaterials.value = response.map((video, index) => ({
				id: video.id || `v-${index}`,
				videoId: video.id || `v-${index}`, // 添加videoId，保持一致性
				title: video.title || `视频${index + 1}`,
				duration: formatVideoDuration(video.duration) || '00:00',
				thumbnailUrl: video.thumbnailUrl || '',
				url: video.url || '',
				selected: false  // 设置为false，确保默认不选中
			}));

			// 删除这一行重复调用，只保留setTimeout内的调用
			// restoreVideoSelectionState();

			// 使用setTimeout延迟恢复选中状态，确保视频列表先被渲染为未选中状态
			setTimeout(() => {
				restoreVideoSelectionState();
				console.log('恢复后选中视频数量:', videoMaterials.value.filter(v => v.selected).length);
			}, 50);

			// ElMessage.success(`已加载 ${response.length} 个视频`)

			// 尝试为没有缩略图但有URL的视频生成缩略图
			// 注意：由于性能考虑，只对前10个视频尝试生成缩略图
			const videoSubset = videoMaterials.value.slice(0, 10).filter(v => v.url && !v.thumbnailUrl);
			if (videoSubset.length > 0) {
				console.log(`尝试为 ${videoSubset.length} 个视频生成缩略图...`);
				extractVideoThumbnails(videoSubset);
			}

			// 返回响应数据，以便其他函数可以使用
			return response;
		} else {
			// ElMessage.warning('未找到视频数据')
			return [];
		}
	} catch (error) {
		console.error('获取视频列表失败:', error)
		// ElMessage.error('获取视频数据失败')
		return [];
	}
}

// 在 script 中添加处理函数
const handleSourceChange = (value) => {
	// 调用 fetchVideoList 获取对应专辑的视频
	fetchVideoList()
}



// 视频预览相关状态
const previewDialogVisible = ref(false)
const currentPreviewUrl = ref('')

// 修复视频预览点击处理函数，避免显示右侧弹窗
const handleVideoPreview = (video) => {
	// 标记为用户操作以确保正确更新
	isUserAction.value = true;

	try {
		// 1. 设置当前预览URL和显示小型预览弹窗
		const videoUrl = video.videoUrl || video.url;
		currentPreviewUrl.value = videoUrl;
		previewDialogVisible.value = true;

		// 将视频URL保存到Pinia store - 添加更详细的日志
		console.log('准备保存视频URL到Pinia store:', videoUrl);
		console.log('保存前检查previewStore实例:', previewStore);



		// 2. 将视频添加到右侧预览区列表，但不显示弹窗
		if (videoUrl) {
			// 创建视频项，确保包含id信息
			const videoItem = {
				name: video.title || '视频素材', // 使用明确的标题
				url: videoUrl,
				thumbnailUrl: video.thumbnailUrl || '',
				id: video.id || video.videoId || `video-${Date.now()}` // 确保包含id信息
			};

			// 更新预览视频列表
			previewVideoList.value = [videoItem];

			// 在控制台中记录，便于调试
			console.log('已添加视频到预览区:', JSON.stringify(previewVideoList.value));

			if (previewStore) {
				console.log('已保存视频URL到Pinia:', previewStore.currentVideoUrl);
			}
		}
	} catch (error) {
		console.error('视频预览处理出错:', error);
	}

	// 恢复标记
	setTimeout(() => {
		isUserAction.value = false;
	}, 100);
};

// 音乐对话框相关状态
const musicDialogVisible = ref(false)

// 处理音乐对话框确认
const handleMusicDialogConfirm = () => {
	// 关闭对话框
	musicDialogVisible.value = false

	// 使用musicStore中的音乐列表更新预览面板的音乐列表
	if (musicStore.musicList && musicStore.musicList.length > 0) {
		// 深拷贝音乐列表，防止直接引用
		previewMusicList.value = JSON.parse(JSON.stringify(musicStore.musicList))
	}
}

// 处理音乐对话框移除
const handleMusicDialogRemove = (index) => {
	// 这里只需同步更新previewMusicList
	previewMusicList.value = JSON.parse(JSON.stringify(musicStore.musicList))
}

// 处理音乐对话框播放切换
const handleMusicDialogTogglePlay = (index) => {
	console.log(`切换播放音乐 ${index}`)
}

// 处理音乐对话框添加音乐
const handleMusicDialogAddMusic = (openDialog = false) => {
	if (openDialog) {
		ElMessage.info('打开音乐对话框')
	} else {
		ElMessage.info('添加音乐')
	}
}

// 获取音乐存储
const musicStore = useMusicStore()

// 获取用户权益管理功能
const { fetchUserBenefits } = useUserBenefits()

// 监听musicStore中的音乐列表变化
watch(() => musicStore.musicList, (newList) => {
	// 当musicStore中的列表变化时，更新previewMusicList
	if (newList) {
		previewMusicList.value = JSON.parse(JSON.stringify(newList))
	}
}, { deep: true })



// 修改handleSmartMatch函数
const handleSmartMatch = async () => {
	// 添加埋点代码
	umeng.trackEvent(
		'视频编辑',
		'点击智能匹配',
		`模式: ${matchMode.value === 1 ? '指定专辑' : '指定视频'}`,
		''
	);

	try {
		// 获取选中的视频
		const selectedVideos = videoMaterials.value.filter(video => video.selected)

		// 检查是否有选中的视频
		if (matchMode.value === 2 && selectedVideos.length === 0) {
			ElMessage.warning('请先选择至少一个视频素材')
			return
		} else if (matchMode.value === 1 && (!selectedAlbumVideos.value || selectedAlbumVideos.value.length === 0)) {
			ElMessage.warning('当前专辑中没有可用的视频素材')
			return
		}

		// 显示加载提示
		// ElMessage.info('正在分析视频时长，请稍候...')

		// 计算文本长度对应的时长并保存到store
		const textDuration = calculateTextDuration();
		if (textDuration > 0) {
			// 更新文本计算的时长到Pinia store
			previewStore.setDubbingDuration(textDuration);
			console.log("文本计算的时长已更新到Pinia:", textDuration, "秒");
		}

		// 获取视频真实时长 - 指定视频模式
		if (matchMode.value === 2 && selectedVideos.length > 0) {
			// 为缺少时长的视频加载时长
			await Promise.all(selectedVideos.map(async (video) => {
				// 如果视频没有时长或时长为null，则通过URL加载获取
				if (!video.duration && video.url) {
					try {
						console.log(`尝试获取视频[${video.title}]时长，URL: ${video.url}`);
						const durationInfo = await loadVideoAndGetDuration(video.url);
						// 更新视频对象的时长
						video.duration = durationInfo.formattedDuration;
						video.durationInSeconds = durationInfo.durationInSeconds;
						console.log(`更新视频[${video.title}]时长: ${video.duration} (${durationInfo.durationInSeconds}秒)`);
					} catch (error) {
						console.error(`获取视频[${video.title}]时长失败:`, error);
					}
				} else {
					console.log(`视频[${video.title}]已有时长: ${video.duration || '未知'}`);
				}
			}));
		}
		// 获取视频真实时长 - 专辑模式
		else if (matchMode.value === 1 && selectedAlbumVideos.value && selectedAlbumVideos.value.length > 0) {
			// 为缺少时长的视频加载时长
			await Promise.all(selectedAlbumVideos.value.map(async (video) => {
				// 如果视频没有时长或时长为null，则通过URL加载获取
				if (!video.duration && video.url) {
					try {
						console.log(`尝试获取专辑视频[${video.title || '未命名'}]时长，URL: ${video.url}`);
						const durationInfo = await loadVideoAndGetDuration(video.url);
						// 更新视频对象的时长
						video.duration = durationInfo.formattedDuration;
						video.durationInSeconds = durationInfo.durationInSeconds;
						console.log(`更新专辑视频[${video.title || '未命名'}]时长: ${video.duration} (${durationInfo.durationInSeconds}秒)`);
					} catch (error) {
						console.error(`获取专辑视频[${video.title || '未命名'}]时长失败:`, error);
					}
				} else {
					console.log(`专辑视频[${video.title || '未命名'}]已有时长: ${video.duration || '未知'}`);
				}
			}));
		}

		// 检查选中视频的时长，决定弹窗内容
		let isLongVideo = false;
		const THIRTY_MINUTES_IN_SECONDS = 1800; // 30分钟 = 1800秒
		let totalDurationInSeconds = 0; // 用于存储所有选中视频的总时长

		// 指定视频模式 - 计算选中视频的总时长
		if (matchMode.value === 2 && selectedVideos.length > 0) {
			totalDurationInSeconds = selectedVideos.reduce((total, video) => {
				// 优先使用直接存储的durationInSeconds
				const durationInSeconds = video.durationInSeconds || durationToSeconds(video.duration || '0:00');
				console.log(`视频[${video.title}]时长: ${video.duration || '未知'}, 换算为秒: ${durationInSeconds}, URL: ${video.url || '无URL'}`);
				return total + durationInSeconds;
			}, 0);

			console.log(`选中视频总时长: ${totalDurationInSeconds}秒 (${Math.floor(totalDurationInSeconds / 60)}分${totalDurationInSeconds % 60}秒)`);

			// 判断总时长是否达到或超过30分钟
			isLongVideo = totalDurationInSeconds >= THIRTY_MINUTES_IN_SECONDS;
		}
		// 指定专辑模式 - 计算专辑视频的总时长
		else if (matchMode.value === 1 && selectedAlbumVideos.value && selectedAlbumVideos.value.length > 0) {
			totalDurationInSeconds = selectedAlbumVideos.value.reduce((total, video) => {
				// 优先使用直接存储的durationInSeconds
				const durationInSeconds = video.durationInSeconds || durationToSeconds(video.duration || '0:00');
				console.log(`专辑视频[${video.title || '未命名'}]时长: ${video.duration || '未知'}, 换算为秒: ${durationInSeconds}, URL: ${video.url || '无URL'}`);
				return total + durationInSeconds;
			}, 0);

			console.log(`专辑视频总时长: ${totalDurationInSeconds}秒 (${Math.floor(totalDurationInSeconds / 60)}分${totalDurationInSeconds % 60}秒)`);

			// 判断总时长是否达到或超过30分钟
			isLongVideo = totalDurationInSeconds >= THIRTY_MINUTES_IN_SECONDS;
		}

		// 计算预计消耗的算粒数量
		const estimatedUnits = calculateComputationUnits();

		// 根据视频时长设置不同的弹窗内容
		if (isLongVideo) {
			// 长视频 - 显示"视频素材较长"的消息
			confirmDialogMessage.value = `您所选取的视频素材较长，可能产生较大的算粒消耗，预计产生<span style="color:#0AAF60">${estimatedUnits}</span>算粒消耗，是否继续生成？`
		} else {
			// 获取选中的视频数量
			const videoCount = matchMode.value === 2
				? videoMaterials.value.filter(video => video.selected).length
				: (selectedAlbumVideos.value ? selectedAlbumVideos.value.length : 0);

			// 短视频 - 显示简单的费用提示，包含视频数量
			confirmDialogMessage.value = `本次选中了 <span style="color:#0AAF60">${videoCount}</span> 个视频，预计生成需耗费 <span style="color:#0AAF60">${estimatedUnits}</span> 算粒，是否继续？`
		}

		// 无论视频长短，都显示确认弹窗
		confirmDialogVisible.value = true

		// 这里提前返回，等待用户在弹窗中选择操作
		return
	} catch (error) {
		console.error('智能匹配出错:', error)
		ElMessage.error('成片生成任务提交失败: ' + (error.message || '未知错误'))
	}
}

// 添加确认匹配动作的处理函数
const handleConfirmMatchAction = async () => {
	try {
		// 同时获取两种模式下的视频列表
		let videosToProcess = [];

		// 指定视频模式
		if (matchMode.value === 2) {
			videosToProcess = videoMaterials.value.filter(video => video.selected);
		}
		// 指定专辑模式
		else if (matchMode.value === 1 && selectedAlbumVideos.value) {
			videosToProcess = selectedAlbumVideos.value;
		}

		// 检查是否有视频可处理
		if (videosToProcess.length === 0) {
			ElMessage.warning('没有选择任何视频');
			return;
		}

		// 检查视频时长，排除时长为0的失效视频
		for (const video of videosToProcess) {
			const duration = video.durationInSeconds || durationToSeconds(video.duration || '0:00');
			if (!duration || duration <= 0) {
				console.error('检测到时长为0的视频:', video.title || '未命名视频');
				ElMessage.error('视频素材失效，请重新选择视频');
				return;
			}
		}

		// 计算需要的算力
		const requiredCompute = calculateComputationUnits();
		console.log('需要消耗的算力:', requiredCompute);

		// 调用检查用户权益API，确认最终消耗的算力
		const userId = getUserId();
		const checkParams = {
			userId: userId,
			feat: "算力",
			need: requiredCompute  // 使用requiredCompute字段传递需要的空间值
		};

		console.log('发送算力检查请求:', checkParams);
		const confirmResult = await checkUploadPermission(checkParams);
		console.log('算力检查结果:', confirmResult);

		// 使用content.result判断算力是否足够
		// 如果result为false，表示算力不足，显示算力不足弹窗
		if (confirmResult && confirmResult.content && confirmResult.content.result === false) {
			console.log('算力不足，result:', confirmResult.content.result);
			// 显示算力不足弹窗
			insufficientComputeDialogVisible.value = true;
			return;
		}
		// result为true表示有足够算力，继续执行合成匹配逻辑

		// 更新Pinia store中的视频URL - 对两种模式统一处理
		if (videosToProcess.length > 0 && videosToProcess[0].url) {
			const videoUrl = videosToProcess[0].url;
			console.log(`智能匹配(${matchMode.value === 2 ? '指定视频' : '专辑'}模式): 更新当前视频URL到Pinia store:`, videoUrl);

			// 保存到Pinia store
			if (previewStore) {
				previewStore.setCurrentVideoUrl(videoUrl);
				console.log('更新后检查Pinia store中的视频URL:', previewStore.currentVideoUrl);
			}
		}

		// 先为所有选中的视频生成缩略图
		console.log('为选中的视频生成缩略图...')
		await Promise.all(videosToProcess.map(video => {
			if (video.url && !video.thumbnailUrl) {
				return new Promise(resolve => {
					// 创建视频元素
					const videoEl = document.createElement('video')
					videoEl.crossOrigin = 'anonymous'
					videoEl.src = video.url
					videoEl.muted = true // 避免声音播放

					// 添加更多事件监听以获取详细状态
					videoEl.addEventListener('loadstart', () => {
						console.log(`缩略图生成 - 视频开始加载: ${video.url}`);
					});

					videoEl.addEventListener('canplay', () => {
						console.log(`缩略图生成 - 视频可以播放: ${video.url}`);
					});

					videoEl.addEventListener('loadeddata', () => {
						// 设置到第一帧
						videoEl.currentTime = 1 // 使用1秒而不是0秒，避免黑屏
						console.log(`缩略图生成 - 视频数据已加载: ${video.url}`);
					})

					let timeUpdateAttempts = 0;
					const maxTimeUpdateAttempts = 3;

					videoEl.addEventListener('timeupdate', function onTimeUpdate() {
						timeUpdateAttempts++;
						console.log(`缩略图生成 - 时间更新事件 (${timeUpdateAttempts}/${maxTimeUpdateAttempts}): ${video.url}`);

						// 如果是第一次触发且当前时间过小，可能还未真正定位到1秒位置
						if (timeUpdateAttempts < maxTimeUpdateAttempts && videoEl.currentTime < 0.5) {
							console.log(`缩略图生成 - 时间定位可能不准确(${videoEl.currentTime}秒)，再次尝试: ${video.url}`);
							return; // 不进行处理，等待下一次timeupdate
						}

						// 移除事件监听器，避免重复处理
						videoEl.removeEventListener('timeupdate', onTimeUpdate)

						// 创建canvas绘制视频帧
						const canvas = document.createElement('canvas')
						canvas.width = videoEl.videoWidth || 320
						canvas.height = videoEl.videoHeight || 180
						const ctx = canvas.getContext('2d')

						try {
							// 绘制视频帧到canvas
							ctx.drawImage(videoEl, 0, 0, canvas.width, canvas.height)
							// 生成缩略图URL
							const thumbnailUrl = canvas.toDataURL('image/jpeg', 0.8)
							// 更新视频对象
							video.thumbnailUrl = thumbnailUrl
							console.log(`已成功生成缩略图: ${video.title || '未命名'}`);
						} catch (e) {
							console.error(`生成缩略图失败: ${e.message}`, e);
						}

						// 释放资源
						videoEl.pause()
						videoEl.src = ''
						videoEl.load() // 确保资源被释放
						resolve()
					})

					// 处理加载失败情况
					videoEl.addEventListener('error', () => {
						console.error('视频加载失败:', video.url)
						resolve()
					})

					// 设置超时
					setTimeout(() => {
						if (!video.thumbnailUrl) {
							console.warn('生成缩略图超时:', video.title)
							resolve()
						}
					}, 5000)

					// 加载视频
					videoEl.load()
				})
			}
			return Promise.resolve()
		}))

		// 获取第一个视频的缩略图作为展示
		if (videosToProcess.length > 0) {
			// 优先使用已有缩略图，如果没有则使用刚生成的
			selectedThumbnailUrl.value = videosToProcess[0].thumbnailUrl || '';
			console.log('选中视频缩略图URL:', selectedThumbnailUrl.value);
		} else if (matchMode.value === 1 && selectedAlbumItem.value) {
			// 专辑模式下使用专辑的缩略图
			selectedThumbnailUrl.value = selectedAlbumItem.value.thumbnailUrl || selectedAlbumItem.value.cover || '';
			console.log('选中专辑缩略图URL:', selectedThumbnailUrl.value);
		}

		// 整合成mediaArray数组
		let mediaArray = [];

		// 统一处理两种模式的视频URL
		if (videosToProcess.length > 0) {
			mediaArray = videosToProcess.map(video => video.url).filter(url => url && typeof url === 'string' && url.trim() !== '');

			// 准备loadingVideos数据用于展示
			loadingVideos.value = videosToProcess.map(video => ({
				title: video.title || '未命名视频',
				thumbnailUrl: video.thumbnailUrl || '',
				url: video.url
			}));

			console.log(`从${matchMode.value === 2 ? '指定视频' : '专辑'}模式中获取到 ${mediaArray.length} 个视频URL`);
		}

		// 检查mediaArray是否有内容
		if (mediaArray.length === 0) {
			ElMessage.warning('没有可用的视频素材');
			return;
		}

		// 再次确保在调用API前，Pinia store中的URL已更新为最新的URL
		if (mediaArray.length > 0) {
			const firstVideoUrl = mediaArray[0];
			if (firstVideoUrl && (!previewStore.currentVideoUrl || previewStore.currentVideoUrl !== firstVideoUrl)) {
				console.log('最终确认: 更新当前视频URL到Pinia store:', firstVideoUrl);
				previewStore.setCurrentVideoUrl(firstVideoUrl);
				console.log('最终确认后的Pinia store视频URL:', previewStore.currentVideoUrl);
			}
		}

		// 获取配音角色信息 - 修改这里，优先使用voiceName而不是name
		const voiceName = previewStore.selectedRole?.voiceName || previewStore.selectedRole?.name || null;
		console.log('获取配音角色:', voiceName);

		// 修复：使用正确的方式获取配音角色音量
		// 由于没有直接访问 PreviewPanel 内部 materialItems 的方法，改用从 previewStore 获取
		let voiceVolume = "0.5"; // 默认值为0.5（对应50%）

		// 如果 previewStore 中有存储音量，则使用该值
		if (previewStore.selectedRole && typeof previewStore.selectedRole.volume === 'number') {
			// 将 0-100 的值转换为 0-1 范围
			voiceVolume = String(previewStore.selectedRole.volume / 100);
		} else {
			// 尝试从localStorage获取
			const storedVolume = localStorage.getItem('soundVolume');
			if (storedVolume) {
				voiceVolume = String(parseInt(storedVolume) / 100);
			}
		}

		console.log('配音角色音量:', voiceVolume);

		// 定义获取文件大小的函数
		const getFileSize = async (url) => {
			try {
				const response = await fetch(url, { method: 'HEAD' });
				const size = response.headers.get('content-length');
				return size ? String(size) : "1024";
			} catch (e) {
				console.error('获取文件大小失败:', e);
				return "1024";
			}
		};

		// 获取所有视频的文件大小
		await Promise.all(videosToProcess.map(async (video) => {
			if (video.url) {
				const size = await getFileSize(video.url);
				video.fileSize = size;
			}
		}));

		// 构建mediaDetail数组 - 只包含选中的视频
		const mediaDetail = videosToProcess.map((video, index) => {
			// 获取视频时长（秒）
			const contentLength = video.durationInSeconds || durationToSeconds(video.duration || '0:00');
			// 获取视频文件大小（如果有）
			const fileSize = video.fileSize || "1024";
			// 计算单个视频消耗的算力
			const minutes = Math.ceil(contentLength / 60); // 转换为分钟并向上取整
			let volum = String(minutes * 3); // 每分钟3算力

			// 如果是第一个视频，添加文本算力
			if (index === 0) {
				// 计算文本算力
				const textDuration = calculateTextDuration();
				const textMinutes = Math.ceil(textDuration / 60);
				const textUnits = textMinutes * 3;
				
				// 将文本算力加到第一个视频的算力中
				const totalVolum = parseInt(volum) + textUnits;
				volum = String(totalVolum);
				console.log(`视频算力: ${minutes * 3}, 文本算力: ${textUnits}, 总算力: ${totalVolum}`);
			}

			return {
				ossUrl: video.url || "",
				contentLength: String(contentLength),
				fileSize: fileSize,
				volum: volum
			};
		});

		// 准备请求参数
		const params = {
			type: '2',
			userId: getUserId(),
			mediaArray: mediaArray,
			mediaDetail: mediaDetail, // 添加mediaDetail数组
			speechConfig: {
				volume: voiceVolume,
				speechRate: "0",
				Voice: voiceName,
				style: null
			}
		};

		// 如果是专辑模式，添加albumId参数
		if (matchMode.value === 1 && selectedAlbumItem.value && selectedAlbumItem.value.albumId) {
			params.albumId = selectedAlbumItem.value.albumId;
		}

		// 添加背景音乐配置 - 新增代码
		// 获取音乐音量
		let musicVolume = 5; // 默认值为5（相当于50%）

		// 优先从Pinia获取音乐音量
		if (musicStore.musicList && musicStore.musicList.length > 0 && typeof musicStore.musicList[0].volume === 'number') {
			// 将0-100范围转换为0-10范围
			musicVolume = musicStore.musicList[0].volume / 10;
			console.log('从Pinia中获取音乐音量:', musicStore.musicList[0].volume,
				'转换后(除以10):', musicVolume,
				'确认: 100=>10, 50=>5');
		}
		// 如果Pinia中没有音量信息，尝试从localStorage获取
		else {
			const storedVolume = localStorage.getItem('musicVolume');
			if (storedVolume) {
				musicVolume = parseInt(storedVolume) / 10;
				console.log('从localStorage获取音乐音量:', storedVolume,
					'转换后(除以10):', musicVolume);
			}
		}

		// 确保音量在0-10范围内
		musicVolume = Math.max(0, Math.min(10, musicVolume));

		// 添加背景音乐配置到参数
		params.backgroundMusicConfig = {
			volume: musicVolume,
			style: null
		};
		console.log('添加背景音乐配置:', params.backgroundMusicConfig);

		// 修改为优先从Pinia获取音乐列表 
		// 添加背景音乐数组参数
		let musicUrls = [];

		// 优先从musicStore获取音乐列表（Pinia存储）
		if (musicStore.musicList && musicStore.musicList.length > 0) {
			musicUrls = musicStore.musicList
				.filter(music => music.url && typeof music.url === 'string' && music.url.trim() !== '')
				.map(music => music.url);

			console.log('从Pinia musicStore获取到音乐URL:', musicUrls);
		}
		// 如果Pinia中没有，则从本地previewMusicList获取
		else if (previewMusicList.value && previewMusicList.value.length > 0) {
			musicUrls = previewMusicList.value
				.filter(music => music.url && typeof music.url === 'string' && music.url.trim() !== '')
				.map(music => music.url);

			console.log('从本地previewMusicList获取到音乐URL:', musicUrls);
		}

		// 添加背景音乐数组到参数
		if (musicUrls.length > 0) {
			params.backgroundMusicArray = musicUrls;
			console.log('添加背景音乐数组到请求:', musicUrls);
		} else {
			console.log('没有找到有效的背景音乐URL');
		}

		// 获取预览内容 - 修改为优先从store获取，然后再使用预览面板中的值
		const effectiveContent = previewStore.content || previewContent.value || '';

		// 如果右侧预览有文字，则添加到speechTextArray
		if (effectiveContent && effectiveContent.trim() !== '') {
			// 将预览内容按行分割，过滤空行，然后合并成一个逗号分隔的字符串
			const combinedText = effectiveContent.split('\n')
				.filter(text => text.trim() !== '')
				.join(',');

			// 将合并后的字符串作为数组的唯一元素
			params.speechTextArray = [combinedText];
			console.log('添加了文本内容:', params.speechTextArray);
		}

		// 获取预览标题 - 同样修改为优先从store获取
		const effectiveTitle = previewStore.title || previewTitle.value || '';

		// 处理标题逻辑
		if (effectiveTitle && effectiveTitle.trim() !== '') {
			// 如果有标题，直接使用
			params.titleArray = [effectiveTitle.trim()];
			console.log('添加了标题:', params.titleArray);
		/* 注释掉从speechTextArray截取标题的逻辑，如果标题没有文字就不需要赋值了
		} else if (params.speechTextArray && params.speechTextArray.length > 0 && params.speechTextArray[0]) {
			// 如果没有标题，但有文本内容，则截取文本前10个字符作为标题
			const text = params.speechTextArray[0];
			// 截取前10个字符，对于中文或其他多字节字符也能正确处理
			const titleFromText = text.slice(0, 10);
			params.titleArray = [titleFromText];
			console.log('未填写标题，从文本内容截取前10个字作为标题:', params.titleArray);
		*/
		}

		console.log('提交成片生成任务参数:', params);

		// 显示视频加载对话框，并设置loadingVideos数据
		loadingDialogVisible.value = true;
		loadingProgress.value = 20; // 初始进度

		// 模拟进度增长
		const progressInterval = setInterval(() => {
			if (loadingProgress.value < 90) {
				loadingProgress.value += 5;
			}
		}, 800);

		// 调用接口
		const response = await submitGenerateJob({ ...params, need_code: true });
		console.log('response:', response);
		// 检查response是否包含code=1的情况（API错误信息）
		if (response && response.code === 1) {
			// 清除进度定时器
			clearInterval(progressInterval);
			// 关闭加载对话框
			loadingDialogVisible.value = false;
			// 显示API返回的错误信息
			ElMessage.error(response.msg || '成片生成任务失败');
			return;
		}

		if (response) {
			// 设置进度为100%
			loadingProgress.value = 100;

			// 清除进度定时器
			clearInterval(progressInterval);
			console.log('response:', response);

			// 正确解析时间线数据结构
			if (response?.data?.getProjectExportJobResponse &&
				Array.isArray(response?.data?.getProjectExportJobResponse) &&
				(response?.data?.getProjectExportJobResponse?.length ?? 0) > 0) {

				try {
					console.log('找到媒体制作作业数据，尝试解析');
					// 获取作业数组
					const jobArray = response?.data?.getProjectExportJobResponse ?? [];
					console.log('作业数组长度:', jobArray.length);

					// 获取第一个作业的时间线数据
					const job = jobArray[0] ?? {};
					console.log('作业状态:', job?.statusCode);

					// 检查是否存在body和projectExportJob结构
					if (job?.body?.projectExportJob?.exportResult) {
						// 获取timeline数据
						const timeline = job?.body?.projectExportJob?.exportResult?.timeline;

						if (timeline) {
							console.log('找到timeline数据');

							// 如果timeline已经是对象（不是字符串），则直接使用
							let timelineJson = timeline;

							// 如果timeline是字符串，则需要解析
							if (typeof timeline === 'string') {
								try {
									console.log('timeline是字符串，尝试解析为JSON');
									timelineJson = JSON.parse(timeline);
									console.log('成功解析timeline JSON');
								} catch (parseError) {
									console.error('解析timeline字符串失败:', parseError);
									// 如果解析失败，直接使用原始字符串
									timelineJson = timeline;
								}
							}

							// 处理解析后的JSON数据，传入作业数组
							processVideoJson(timelineJson, jobArray).then(result => {
								extractedContentList.value = result;
								console.log('提取内容完成，共提取条目:', extractedContentList.value.length);

								// 重要：确保明确保存到Pinia
								if (extractedContentList.value && extractedContentList.value.length > 0) {
									console.log('保存智能匹配提取内容到Pinia前:', extractedContentList.value.length);

									try {
										// 使用深拷贝确保数据独立，避免引用问题
										const contentToStore = JSON.parse(JSON.stringify(extractedContentList.value));
										previewStore.setExtractedContentList(contentToStore);

										// 确认保存成功
										console.log('保存智能匹配提取内容后检查:', {
											piniaSaved: Boolean(previewStore.extractedContentList?.length),
											length: previewStore.extractedContentList?.length || 0,
											first: previewStore.extractedContentList && previewStore.extractedContentList.length > 0
												? previewStore.extractedContentList[0].name
												: 'none'
										});

										// 设置时间线标记
										previewStore.setIsTimelineContent(true);
									} catch (e) {
										console.error('保存提取内容到Pinia失败:', e);
									}
								}
							}).catch(error => {
								console.error('处理视频JSON数据时出错:', error);
							});
						}
					} else {
						console.warn('作业数据中没有timeline字段，可用字段:', Object.keys(job));
					}
				} catch (e) {
					console.error('解析视频内容JSON失败:', e);
					console.error('错误栈:', e.stack);
				}
			} else {
				console.log('响应中没有媒体制作作业数据，响应结构:', Object.keys(response));
			}

			// 延时执行，让进度条显示完成状态
			setTimeout(() => {
				// 关闭加载对话框
				loadingDialogVisible.value = false;

				// 设置视频已生成状态
				videoGenerated.value = true;

				// 确保提取内容会显示 - 只有在有提取内容时才设置时间线标记
				if (previewStore) {
					// 检查提取内容是否已保存到Store中
					if (extractedContentList.value && extractedContentList.value.length > 0) {
						console.log('视频生成成功，有提取内容，设置时间线内容标记为true');
						previewStore.setIsTimelineContent(true);

						console.log('视频生成成功，确保提取内容已保存到Store:', extractedContentList.value.length);

						// 使用JSON深拷贝再次保存，确保数据被正确写入
						try {
							const contentToStore = JSON.parse(JSON.stringify(extractedContentList.value));
							previewStore.setExtractedContentList(contentToStore);
						} catch (e) {
							console.error('重新保存提取内容到Store时出错:', e);
						}
					} else {
						console.warn('没有有效的提取内容可以显示，设置时间线内容标记为false');
						previewStore.setIsTimelineContent(false);
						// 清除localStorage中的标记，避免误显示
						localStorage.removeItem('showExtractedContent');
					}
				}

				// 自动切换到预览工具
				activeTool.value = 2;

				// 获取预览视频并在加载完成后显示成功消息
				isLoadingPreview.value = true;

				fetchPreviewVideos().then(async () => {
					// 确保在视频加载完成后选中第一个视频
					if (thumbnailVideos.value.length > 0) {
						console.log('自动选中第一个预览视频');
						activeVideoIndex.value = 0;
						selectVideo(0);
					}

					// 显示成功消息
					// ElMessage.success('视频生成成功，已自动切换到预览模式');
					isLoadingPreview.value = false;

					// 智能匹配完成所有接口调用后，更新用户权益
					try {
						console.log('智能匹配完成，正在更新用户权益...');
						await fetchUserBenefits();
						console.log('用户权益更新完成');
					} catch (error) {
						console.error('更新用户权益失败:', error);
						// 权益更新失败不影响主流程，仅记录错误
					}
				}).catch(error => {
					console.error('预览视频加载失败:', error);
					ElMessage.error('预览视频加载失败，请重试');
					isLoadingPreview.value = false;
				});

				// 保存当前activeTool的值到localStorage以便下次访问时恢复
				localStorage.setItem('videoEditing_activeTool', '2');

				// 只有在有提取内容时才添加显示提取内容的标记
				if (extractedContentList.value && extractedContentList.value.length > 0) {
					localStorage.setItem('showExtractedContent', 'true');
				}
			}, 1000);

			// 在成功提交后添加详细日志
			console.log('成片生成任务提交成功，完整响应:', JSON.stringify(response));
		} else {
			// 清除进度定时器
			clearInterval(progressInterval);

			// 关闭加载对话框
			loadingDialogVisible.value = false;

			// 设置视频生成状态为false
			videoGenerated.value = false;

			ElMessage.error('成片生成任务提交失败');
		}
	} catch (error) {
		// 关闭加载对话框
		loadingDialogVisible.value = false;

		console.error('智能匹配出错:', error);
		ElMessage.error('成片生成任务提交失败: ' + (error.message || '未知错误'));
	}
}



// 处理加载对话框关闭
const handleLoadingDialogClose = () => {
	loadingDialogVisible.value = false
	// ElMessage.info('视频将在后台继续生成')
}

// 监听预览内容变化
watch(() => previewContent.value, (newContent) => {
	console.log('预览内容变化:', newContent)
}, { immediate: true })

const previewStore = usePreviewStore()

// 在 script 部分的适当位置添加这几个变量声明
// 视频加载对话框状态
const loadingDialogVisible = ref(false)
const loadingProgress = ref(20) // 初始进度
const selectedThumbnailUrl = ref('') // 视频缩略图URL
const loadingVideos = ref([]) // 用于加载对话框中显示的视频列表
// 添加视频是否已生成状态的变量
const videoGenerated = ref(false)

// 智能成片确认弹窗状态
const confirmDialogVisible = ref(false)
const confirmDialogMessage = ref('')

// 扣费说明弹窗控制变量
const feeExplanationDialogVisible = ref(false)
const feeExplanationMessage = ref(`
  亲爱的用户：<br><br>
  按输入和输出总视频时长计费，每分钟消耗30算粒。如将一个时长为90秒的视频作为视频素材，通过一键成片任务成功地输出了一个23秒的成片，根据计费规则，输入和输出的视频总长为(90+23) /60 分钟，并向上取整按2分钟计为总时长，按每分钟30算粒计费，则总共消耗2×30=60算粒。
`)

// 添加算粒不足警告弹窗
const insufficientComputeDialogVisible = ref(false)
const insufficientComputeMessage = ref('<span style="font-weight: bold; font-size: 16px;">当前算粒余额不足</span><br><span style="margin-top: 10px; display: block;">如需继续使用请购买算粒额度！</span>')

// 处理购买算力
const handleBuyCompute = () => {
	insufficientComputeDialogVisible.value = false
	// 这里可以添加跳转到购买算力页面的逻辑
	console.log('跳转到购买算粒页面')
	// 在新标签页打开购买算力页面
	const route = router.resolve({ name: 'membership', query: { nav: 'calculate' } });
	window.open(route.href, '_blank');
}

// 添加空间不足对话框相关变量
const insufficientSpaceDialogVisible = ref(false);
const insufficientSpaceTitle = ref('您的个人空间容量已不足');
const insufficientSpaceMessage = ref('如需使用请购买空间额度！');

// 处理购买空间
const handleBuySpace = () => {
	insufficientSpaceDialogVisible.value = false;
	// 在新标签页打开购买空间页面
	const route = router.resolve({ name: 'membership', query: { nav: 'space' } });
	window.open(route.href, '_blank');
}

// 处理取消购买
const handleCancelBuy = () => {
	insufficientSpaceDialogVisible.value = false;
}




/**
 * 获取预览视频列表
 * @param {string} jobId - 可选的作业ID
 * @param {string} id - 可选的视频ID，用于筛选特定视频
 * @returns {Promise<void>}
 * @description 从服务器获取预览视频并格式化为可用数据，更新到缩略图视频列表
 */
const fetchPreviewVideos = async (jobId, id) => {
	// 返回一个Promise，以便在调用处可以使用then/catch
	return new Promise(async (resolve, reject) => {
		try {
			// 设置加载状态
			isLoadingPreview.value = true;
			// ElMessage.info('正在获取预览视频数据...');

			// 准备API参数
			const apiParams = { userId: getUserId() };

			// 如果提供了id参数，添加到API请求中
			if (id) {
				apiParams.id = id;
				console.log('使用id参数获取特定视频:', id);
			}

			// 调用getVideos接口获取预览视频列表
			const response = await getVideos(apiParams);
			console.log('预览接口返回原始数据:', response);

			if (response && Array.isArray(response) && response.length > 0) {
				// 设置视频已生成状态为true
				videoGenerated.value = true;

				// 记录左侧当前选中的视频IDs，确保稍后能恢复选中状态
				const currentSelectedVideos = videoMaterials.value.filter(v => v.selected);
				const selectedVideoIds = currentSelectedVideos.map(v => v.id || v.videoId);
				console.log('保存当前选中的视频IDs:', selectedVideoIds);

				// 更新缩略图视频列表，确保保留ossPath字段和正确处理duration
				thumbnailVideos.value = response.map((video, index) => ({
					id: video.id || index + 1,
					videoId: video.id || `preview-${index}`, // 添加videoId，保持一致性
					title: video.title || video.name || `视频${index + 1}`,
					url: video.url || '',
					thumbnailUrl: video.thumbnailUrl || '',
					videoUrl: video.videoUrl || video.url || '',
					ossPath: video.ossPath || video.url || '',
					color: video.color || '#3D4553',
					createTime: video.createTime || '',
					// 确保正确格式化视频时长
					duration: formatVideoDuration(video.duration)
				}));

				console.log('格式化后的预览视频数据:', thumbnailVideos.value);

				// 如果有缩略图视频，默认选中第一个视频
				if (thumbnailVideos.value.length > 0) {
					console.log('自动选中第一个预览视频');
					activeVideoIndex.value = 0;
					// 调用selectVideo函数来选择和加载第一个视频
					selectVideo(0);

					// 确保视频URL已更新到Pinia store
					const firstVideoUrl = thumbnailVideos.value[0].ossPath || thumbnailVideos.value[0].url || thumbnailVideos.value[0].videoUrl;
					if (firstVideoUrl) {
						previewStore.setCurrentVideoUrl(firstVideoUrl);
						console.log('更新预览视频URL到Pinia store:', firstVideoUrl);
					}

					// 如果已有提取内容，为每个提取内容项生成缩略图
					if (previewStore.isTimelineContent &&
						extractedContentList.value && extractedContentList.value.length > 0) {
						console.log('准备为提取内容生成缩略图');

						try {
							// 准备视频快照参数
							const snapshotParams = previewStore.getVideoSnapshotParams();
							if (snapshotParams && snapshotParams.storypath && snapshotParams.times) {
								// 使用store中的辅助方法获取时间点
								console.log('开始生成视频快照', snapshotParams);

								// 调用生成视频快照的API
								const snapResponse = await generateVideoSnapshotUrls(snapshotParams);
								console.log('视频快照生成成功, 响应类型:', typeof snapResponse, '内容:',
									Array.isArray(snapResponse) ? `数组(长度:${snapResponse.length})` : snapResponse);
								if (snapResponse && snapResponse.length > 0) {
									console.log('第一项示例:', JSON.stringify(snapResponse[0]));
								}

								// 处理返回的快照URL
								if (snapResponse && Array.isArray(snapResponse) && snapResponse.length > 0) {
									// 更新extractedContentList中的缩略图URL
									updateExtractedContentThumbnails(snapResponse);
								}
							}
						} catch (error) {
							console.error('生成视频快照失败:', error);
							// 失败不影响整体流程，继续执行
						}
					}
				}
			} else {
				// 如果没有视频数据，将视频生成状态设为false
				videoGenerated.value = false;
				ElMessage.warning('没有找到可预览的视频数据');
			}

			// 成功时解析Promise
			resolve(thumbnailVideos.value);
		} catch (error) {
			console.error('获取预览视频失败:', error);
			// ElMessage.error('获取预览视频数据失败');
			// 确保错误时清空缩略图列表
			thumbnailVideos.value = [];
			// 失败时拒绝Promise
			reject(error);
		} finally {
			// 无论成功或失败，都关闭加载状态
			isLoadingPreview.value = false;
		}
	});
};

/**
 * 更新提取内容的缩略图
 * @param {Array} snapshots - 从API获取的快照URL数组
 */
const updateExtractedContentThumbnails = (snapshots) => {
	if (!extractedContentList.value || !Array.isArray(snapshots) || snapshots.length === 0) {
		return;
	}

	console.log('更新提取内容缩略图，有 ', snapshots.length, '个快照和',
		extractedContentList.value.length, '个提取内容项');
	console.log('快照数据示例:', snapshots[0]);

	// 提取快照URL
	const snapshotUrls = snapshots.map(item => {
		// 检查是否为对象且有url属性
		if (item && typeof item === 'object' && item.url) {
			return item.url;
		}
		// 如果是字符串，则直接使用
		else if (typeof item === 'string') {
			return item;
		}
		return null;
	}).filter(url => url); // 过滤掉null值

	console.log('提取的快照URLs:', snapshotUrls);

	// 按时间点匹配快照URL和提取内容
	extractedContentList.value.forEach((item, index) => {
		if (index < snapshotUrls.length && snapshotUrls[index]) {
			item.thumbnailUrl = snapshotUrls[index];
			console.log(`设置第${index + 1}个内容项的缩略图:`, snapshotUrls[index]);
		}
	});

	// 更新存储在Pinia中的提取内容列表
	const contentToStore = JSON.parse(JSON.stringify(extractedContentList.value));
	previewStore.setExtractedContentList(contentToStore);
	console.log('成功更新提取内容缩略图');
};

/**
 * 下载当前选中的视频
 * @returns {void}
 * @description 将当前播放器中的视频文件下载到用户本地，自动提取文件名
 */
const downloadVideo = () => {
	// 检查是否有选中的视频
	if (!videoPlayer.value || !videoPlayer.value.src) {
		ElMessage.warning('没有可下载的视频')
		return
	}

	try {
		// 创建一个a标签用于下载
		const a = document.createElement('a')
		// 将 http URL 转换为 https URL
		let videoUrl = videoPlayer.value.src
		if (videoUrl.startsWith('http:')) {
			videoUrl = videoUrl.replace('http:', 'https:')
		}
		a.href = videoUrl
		console.log('下载视频URL:', videoUrl)
		// 从URL中提取文件名，如果无法提取则使用默认名称
		let filename = 'video.mp4'
		try {
			// 尝试从URL中获取文件名
			const url = new URL(videoPlayer.value.src)
			const pathname = url.pathname
			const lastSlashIndex = pathname.lastIndexOf('/')
			if (lastSlashIndex !== -1 && lastSlashIndex < pathname.length - 1) {
				const extractedName = pathname.substring(lastSlashIndex + 1)
				if (extractedName) {
					filename = extractedName
				}
			}
		} catch (e) {
			console.error('无法从URL解析文件名:', e)
		}

		// 如果有选中的视频缩略图，使用其标题作为文件名
		if (activeVideoIndex.value >= 0 && activeVideoIndex.value < thumbnailVideos.value.length) {
			const videoTitle = thumbnailVideos.value[activeVideoIndex.value].title
			if (videoTitle) {
				// 确保文件名以.mp4结尾
				if (!filename.toLowerCase().endsWith('.mp4')) {
					filename = `${videoTitle}.mp4`
				} else {
					filename = `${videoTitle}.mp4`
				}
			}
		}

		a.download = filename
		document.body.appendChild(a)
		a.click()
		document.body.removeChild(a)

		// ElMessage.success(`正在下载视频: ${filename}`)
	} catch (error) {
		console.error('下载视频失败:', error)
		ElMessage.error('下载视频失败，请稍后重试')
	}
}

// 加入空间对话框状态
const spaceDialogVisible = ref(false)
// 默认收藏夹列表
const spaceFolders = ref([
	{ name: "默认收藏夹", count: 0 },
	{ name: "视频素材", count: 3 },
	{ name: "项目收藏", count: 7 }
])
// 默认选中的收藏夹
const selectedSpaceFolders = ref(["默认收藏夹"])



// 处理空间对话框确认
const handleSpaceDialogConfirm = (folders) => {
	if (!folders || folders.length === 0) {
		ElMessage.warning('请选择至少一个收藏夹')
		return
	}

	// 获取当前选中的视频标题
	let videoTitle = '视频'
	if (activeVideoIndex.value >= 0 && activeVideoIndex.value < thumbnailVideos.value.length) {
		videoTitle = thumbnailVideos.value[activeVideoIndex.value].title || '视频'
	}

	// 显示成功消息
	// ElMessage.success(`已将"${videoTitle}"添加到 ${folders.join(', ')}`)
}






// 添加选中专辑的状态变量
const selectedAlbumItem = ref(null) // 存储选中的专辑项的ID或索引

// 添加视频时长格式化函数，确保时长展示正确
const formatVideoDuration = (duration) => {
	// 如果已经是格式化的字符串，则直接返回
	if (typeof duration === 'string' && duration.includes(':')) {
		return duration;
	}

	// 如果是数字（秒数），则格式化为 MM:SS
	if (typeof duration === 'number' || !isNaN(Number(duration))) {
		const totalSeconds = Number(duration);
		const minutes = Math.floor(totalSeconds / 60);
		const seconds = Math.floor(totalSeconds % 60);
		return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
	}

	// 如果没有有效值，则返回默认值
	return '00:00';
};

/**
 * 获取并解析SRT文件内容
 * @param {string} srtUrl - SRT文件的URL
 * @returns {Promise<string>} - 解析后的字幕文本内容
 * @description 从指定URL获取SRT字幕文件并解析为纯文本内容
 */
const fetchAndParseSrtContent = async (srtUrl) => {
	if (!srtUrl) return '';
	
	// 将 http URL 转换为 https URL
	if (srtUrl.startsWith('http:')) {
		srtUrl = srtUrl.replace('http:', 'https:');
	}

	try {
		console.log('开始获取SRT文件内容:', srtUrl);

		// 发送请求获取SRT文件内容
		const response = await fetch(srtUrl, {
			method: 'GET',
			mode: 'cors', // 添加跨域支持
			headers: {
				'Content-Type': 'text/plain',
			}
		});

		if (!response.ok) {
			throw new Error(`获取SRT文件失败: ${response.status} ${response.statusText}`);
		}

		// 获取文本内容
		const srtContent = await response.text();
		console.log('获取到SRT内容:', srtContent.substring(0, 100) + '...');

		// 解析SRT内容
		const subtitleText = parseSrtContent(srtContent);
		console.log('解析后的字幕文本:', subtitleText);

		return subtitleText;
	} catch (error) {
		console.error('获取或解析SRT文件失败:', error);
		return ''; // 失败时返回空字符串
	}
};

/**
 * 解析SRT内容，提取字幕文本
 * @param {string} srtContent - SRT文件内容
 * @returns {string} - 合并后的字幕文本
 * @description 将SRT格式的字幕内容解析为纯文本，去除时间戳和序号，保留标点符号
 */
const parseSrtContent = (srtContent) => {
	if (!srtContent) return '';

	try {
		// 将SRT内容按块分割（空行分隔）
		const blocks = srtContent.trim().split(/\r?\n\r?\n/);
		console.log(`找到${blocks.length}个SRT块`);

		// 提取每个块中的文本内容
		const textLines = [];

		blocks.forEach(block => {
			const lines = block.split(/\r?\n/);
			// SRT格式：1. 序号 2. 时间范围 3+. 文本内容（可能有多行）
			if (lines.length >= 3) {
				// 跳过前两行（序号和时间范围），提取剩余所有行作为文本
				const textContent = lines.slice(2).join(' ');
				textLines.push(textContent);
			}
		});

		// 合并所有文本行，使用中文逗号作为分隔符
		const combinedText = textLines.join('，');
		console.log(`解析出${textLines.length}行字幕文本`);

		return combinedText;
	} catch (error) {
		console.error('解析SRT内容失败:', error);
		return '';
	}
};

/**
 * 提取TTS内容和播放时间
 * @param {Object} jsonData - 包含字幕轨道信息的JSON数据
 * @param {Array} [jobArray=[]] - 可选的作业数组
 * @returns {Promise<Array>} - 提取的内容数组，包含文本和时间信息
 * @description 从视频JSON数据中提取字幕内容，并保留时间信息
 */
const extractTtsContentWithTime = async (jsonData, jobArray = []) => {
	// 创建结果数组
	const result = [];

	// 获取作业数组长度，如果未提供则默认为0
	const jobArrayLength = jobArray?.length || 0;
	console.log(`作业数组长度: ${jobArrayLength}`);

	// 如果没有JSON数据，直接返回空数组
	if (!jsonData || !jsonData.SubtitleTracks) {
		console.log('JSON数据无效或没有SubtitleTracks，返回空结果');
		return [];
	}

	console.log('开始提取内容...');

	// 临时存储提取的文本
	let extractedTexts = [];

	// 从SubtitleTracks提取字幕内容
	if (jsonData.SubtitleTracks && Array.isArray(jsonData.SubtitleTracks) && jsonData.SubtitleTracks.length > 0) {
		console.log(`找到${jsonData.SubtitleTracks.length}个字幕轨道，提取字幕内容`);

		// 使用forEach和Promise.all处理异步操作
		const promises = [];

		jsonData.SubtitleTracks.forEach((track, trackIndex) => {
			if (track.SubtitleTrackClips && Array.isArray(track.SubtitleTrackClips)) {
				console.log(`字幕轨道${trackIndex}包含${track.SubtitleTrackClips.length}个字幕片段`);

				track.SubtitleTrackClips.forEach(clip => {
					// 只处理有SRT文件URL的片段
					if (clip.FileUrl) {
						// 将 http URL 转换为 https URL
						if (clip.FileUrl.startsWith('http:')) {
							clip.FileUrl = clip.FileUrl.replace('http:', 'https:');
						}
						console.log(`发现SRT文件URL: ${clip.FileUrl}`);

						// 创建解析SRT的Promise
						const promise = fetchAndParseSrtContent(clip.FileUrl).then(srtText => {
							// 只有成功解析到SRT内容时才添加
							if (srtText) {
								console.log(`从SRT文件解析到字幕内容: "${srtText.substring(0, 50)}..."`);
								// 处理字幕文本，将换行符替换为空格，避免生成<br>标签
								const processedText = srtText.replace(/\n/g, ' ').trim();
								extractedTexts.push({
									name: processedText,
									time: {
										start: clip.TimelineIn || 0,
										end: clip.TimelineOut || 0,
										duration: (clip.TimelineOut || 0) - (clip.TimelineIn || 0)
									}
								});
							}
						});

						promises.push(promise);
					}
				});
			}
		});

		// 等待所有SRT解析完成
		if (promises.length > 0) {
			await Promise.all(promises);
		}

		console.log(`从SubtitleTracks提取了${extractedTexts.length}条内容`);

		// ====== 重要: 在这里对提取的文本进行排序 ======
		console.log('排序前的内容:', extractedTexts.map(item => ({
			text: item.name.substring(0, 20),
			start: item.time?.start,
			end: item.time?.end
		})));

		// 对时间进行排序 - 直接按原始值排序
		extractedTexts.sort((a, b) => {
			// 使用安全的数字转换
			const startA = a.time && a.time.start !== undefined ? parseFloat(a.time.start) : 0;
			const startB = b.time && b.time.start !== undefined ? parseFloat(b.time.start) : 0;

			// 检查并记录时间值
			if (isNaN(startA) || isNaN(startB)) {
				console.warn('发现无效的时间值:', { startA, startB, a, b });
				return 0;
			}

			console.log(`比较时间: ${startA} vs ${startB} - 文本: "${a.name.substring(0, 15)}..." vs "${b.name.substring(0, 15)}..."`);
			return startA - startB;
		});

		console.log('排序后的内容:', extractedTexts.map(item => ({
			text: item.name.substring(0, 20),
			start: item.time?.start,
			end: item.time?.end
		})));

		// 将排序后的内容添加到结果数组
		result.push(...extractedTexts);

		// 保存到Pinia
		try {
			// 合并所有字幕文本，使用逗号作为分隔符
			const combinedText = extractedTexts.map(item => item.name.trim()).join('，');
			console.log('合并后的字幕文本:', combinedText);

			// 保存到Pinia
			previewStore.setContent(combinedText);

			// 将提取内容列表也保存到Pinia - 使用深拷贝避免引用问题
			// 直接保存原始内容，不替换换行符，保持原始格式
			previewStore.setExtractedContentList(JSON.parse(JSON.stringify(extractedTexts)));

			// 设置标记，表示这是时间线内容
			previewStore.setIsTimelineContent(true);

			// 更新预览面板，传递处理过的文本
			if (previewPanel.value) {
				previewPanel.value.handleContentExtract(combinedText);
			}

			// 更新本地状态 - 也使用原始内容不做处理
			extractedContentList.value = JSON.parse(JSON.stringify(extractedTexts));
		} catch (error) {
			console.error('保存字幕内容到Pinia失败:', error);
		}
	} else {
		console.log('未找到SubtitleTracks或SubtitleTracks为空');
	}

	console.log(`最终结果包含 ${result.length} 条内容，作业数量: ${jobArrayLength}`, result);
	return result;
};

/**
 * 处理视频JSON数据
 * @param {Object} jsonData - 视频JSON数据
 * @param {Array} [jobArray=null] - 可选的作业数组
 * @returns {Promise<Array>} - 从JSON中提取的内容列表
 * @description 解析视频JSON数据，提取字幕内容和时间信息
 */
const processVideoJson = async (jsonData, jobArray = null) => {
	try {
		console.log('开始处理视频JSON数据:', jsonData);

		// 控制是否使用假数据的标记，后端接口准备好后设为false
		const useMockData = true;

		// 检查是否有SubtitleTracks且其中有FileUrl
		const hasValidSubtitles = (data) => {
			if (!data.SubtitleTracks || !Array.isArray(data.SubtitleTracks)) return false;

			for (const track of data.SubtitleTracks) {
				if (!track.SubtitleTrackClips || !Array.isArray(track.SubtitleTrackClips)) continue;

				for (const clip of track.SubtitleTrackClips) {
					if (clip.FileUrl) return true;
				}
			}

			return false;
		};


		// 检查是否包含SubtitleTracks且有FileUrl
		if (hasValidSubtitles(jsonData)) {
			console.log('发现包含FileUrl的SubtitleTracks结构，开始处理字幕内容');
			return await extractTtsContentWithTime(jsonData, jobArray);
		}

		// 如果没有SubtitleTracks或没有带FileUrl的字幕
		console.log('未找到带FileUrl的SubtitleTracks字幕轨道，返回空结果');
		return [];
	} catch (error) {
		console.error('处理视频JSON数据时出错:', error, error.stack);
		return [];
	}
};



// 在组件状态部分添加一个新的ref变量存储提取的内容
const extractedContentList = ref([]);

// 生成随机颜色函数
const getRandomColor = () => {
	// 生成随机的RGB颜色
	const r = Math.floor(Math.random() * 200 + 55); // 55-255之间，避免太暗
	const g = Math.floor(Math.random() * 200 + 55);
	const b = Math.floor(Math.random() * 200 + 55);
	return `rgb(${r}, ${g}, ${b})`;
};

/**
 * 初始化视频素材数据
 * @param {Array} [videoData=null] - 可选的视频数据
 * @returns {void}
 * @description 初始化视频素材数据，确保每个素材项都有selected属性设为false
 */
const initVideoMaterials = (videoData = null) => {
	if (videoData && Array.isArray(videoData)) {
		// 如果传入了数据，直接使用
		videoMaterials.value = videoData.map(item => ({
			...item,
			selected: false, // 明确设置为false，确保默认不选中
			thumbnailUrl: item.thumbnailUrl || '',
			color: getRandomColor()
		}));

		// 不再使用setTimeout延迟，直接调用恢复选中状态
		restoreVideoSelectionState();

		console.log('从传入数据初始化视频素材成功', videoMaterials.value);
		return;
	}

	// 如果没有传入数据，则调用API获取（兼容旧的调用方式）
	console.log('没有传入视频数据，从API获取...');
	fetchVideoList().then(response => {
		// 注意：fetchVideoList已经处理了videoMaterials的更新，
		// 这里主要是为了兼容旧的调用方式，实际上不需要再做额外处理
		console.log('通过API加载视频素材成功');
		// API加载完成后直接恢复状态，不使用延时
		restoreVideoSelectionState();
	}).catch(error => {
		console.error('获取视频素材失败:', error);
		ElMessage.error('获取视频素材列表失败');
	});
};

// 添加新方法处理取消视频选中状态
const cancelVideoSelection = (removedVideo) => {
	console.log('尝试取消视频选中状态，接收到的数据:', removedVideo);

	// 确保removedVideo存在且包含必要信息
	if (!removedVideo) {
		console.warn('取消选中失败: 无效的视频数据');
		return;
	}

	// 优先使用videoId作为唯一标识进行匹配
	const videoId = removedVideo.id;
	let matchedIndex = -1;

	// 首先尝试通过ID进行精确匹配
	if (videoId) {
		matchedIndex = videoMaterials.value.findIndex(video =>
			video.id === videoId ||
			video.videoId === videoId
		);
	}

	// 如果ID匹配失败，尝试通过URL或标题进行匹配
	if (matchedIndex === -1) {
		// URL匹配
		if (removedVideo.url) {
			matchedIndex = videoMaterials.value.findIndex(video =>
				video.url === removedVideo.url ||
				video.videoUrl === removedVideo.url
			);
		}

		// 标题匹配
		if (matchedIndex === -1 && (removedVideo.name || removedVideo.title)) {
			const videoName = removedVideo.name || removedVideo.title;
			matchedIndex = videoMaterials.value.findIndex(video =>
				video.title === videoName ||
				video.name === videoName
			);
		}
	}

	// 如果找到匹配项，只取消该项的选中状态
	if (matchedIndex !== -1) {
		console.log(`找到匹配视频，索引: ${matchedIndex}`);

		// 只取消匹配的视频选中状态，不影响其他视频
		if (videoMaterials.value[matchedIndex]) {
			videoMaterials.value[matchedIndex].selected = false;
			console.log(`已取消选中视频: ${videoMaterials.value[matchedIndex].title}`);
		}

		// 更新选中计数
		updateSelectedCount();

		// 如果是全选状态且现在不再是全选，则更新全选状态
		if (isAllSelected.value && selectedCount.value !== videoMaterials.value.length) {
			isAllSelected.value = false;
		}

		// 同步更新Pinia store中的selectedVideoIds，确保ID一致性
		// 只移除匹配的ID，保留其他ID
		if (previewStore.selectedVideoIds && previewStore.selectedVideoIds.length > 0) {
			const removedId = videoId;
			if (removedId) {
				const updatedIds = previewStore.selectedVideoIds.filter(id => id !== removedId);
				previewStore.setSelectedVideoIds(updatedIds);
				console.log(`已从selectedVideoIds中移除ID: ${removedId}`);
			}
		}
	} else {
		console.warn('未能找到匹配的视频:', {
			url: removedVideo.url,
			id: removedVideo.id,
			name: removedVideo.name || removedVideo.title
		});
	}
}

// 在选择视频时确保保存到Pinia
const updateSelectedVideos = (selectedVideos) => {
	console.log('更新选中的视频:', selectedVideos);

	// 更新本地状态
	previewVideoList.value = [...selectedVideos];

	// 重要：确保保存到Pinia store中，以便路由跳转后仍能恢复
	if (previewStore && typeof previewStore.setVideoList === 'function') {
		// 使用深拷贝防止引用问题
		const videosToStore = JSON.parse(JSON.stringify(selectedVideos));
		console.log('将选中的视频保存到Pinia store:', videosToStore.length);
		previewStore.setVideoList(videosToStore);

		// 重要：同时更新selectedVideoIds，确保两者保持同步
		const selectedIds = selectedVideos.map(v => v.id || `video-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
		console.log('更新selectedVideoIds:', selectedIds);
		previewStore.setSelectedVideoIds(selectedIds);

		// 验证是否成功保存
		console.log('保存后检查Pinia中的视频列表:', {
			storeListLength: previewStore.videoList?.length || 0,
			isArray: Array.isArray(previewStore.videoList),
			selectedIdsLength: previewStore.selectedVideoIds?.length || 0,
			sample: previewStore.videoList && previewStore.videoList.length > 0
				? previewStore.videoList[0].name || '未命名视频'
				: 'none'
		});
	}

	// 通知父组件
	emit('update:videoList', selectedVideos);
}




// 添加路由离开守卫
onBeforeRouteLeave((to, from, next) => {
	console.log('离开VideoEditing页面，保存视频选中状态');

	// 保存选中的视频ID到Pinia
	const selectedVideos = videoMaterials.value.filter(v => v.selected);
	const selectedIds = selectedVideos.map(v => v.videoId || v.id);

	// 保存ID和视频数据
	previewStore.setSelectedVideoIds(selectedIds);

	// 保存选中的视频数据，包含ID
	const videoDataToSave = selectedVideos.map(v => ({
		name: v.title || '视频素材',
		url: v.url || v.videoUrl || '',
		thumbnailUrl: v.thumbnailUrl || '',
		id: v.videoId || v.id  // 保存ID信息
	}));
	previewStore.setVideoList(videoDataToSave);

	console.log(`已保存 ${selectedVideos.length} 个选中视频的状态`);
	next();
});

// 在组件中找到defineEmits的定义
// 先查找定义
const emit = defineEmits(['update:videoList', /* other events */]);



// 添加一个新方法，用于在组件加载时恢复视频选中状态
const restoreVideoSelectionState = () => {
	// 获取 Pinia 中保存的选中视频 ID
	const selectedIds = previewStore.selectedVideoIds || [];
	// 获取 Pinia 中保存的视频列表
	const storedVideoList = previewStore.videoList || [];

	// 优化：如果没有选中ID，快速返回
	if (selectedIds.length === 0) {
		console.log('没有找到已保存的视频选中状态，保持所有视频未选中');
		return;
	}

	console.log('尝试恢复选中状态:', {
		selectedIds,
		storedVideoCount: storedVideoList.length
	});

	// 创建ID的查找集合，提高匹配效率
	const idSet = new Set(selectedIds);

	let restoredCount = 0;

	// 只遍历一次视频材料，进行ID匹配
	videoMaterials.value.forEach((video, index) => {
		// 重置选中状态
		video.selected = false;

		// 获取视频的所有可能ID
		const videoIdOptions = [
			video.videoId,
			video.id,
			`v-${index}`
		].filter(Boolean);

		// 检查是否有任何ID在选中集合中
		const isIdMatch = videoIdOptions.some(id => idSet.has(id));

		// 只根据ID匹配来设置选中状态，不再使用URL匹配
		if (isIdMatch) {
			video.selected = true;
			restoredCount++;
		}
	});

	// 更新选中数量
	updateSelectedCount();

	// 更新预览区域的视频列表
	if (restoredCount > 0) {
		// 获取所有选中的视频
		const selectedVideos = videoMaterials.value.filter(v => v.selected);

		// 更新预览列表
		previewVideoList.value = selectedVideos.map(v => ({
			name: v.title || '视频素材',
			url: v.url || v.videoUrl || '',
			thumbnailUrl: v.thumbnailUrl || '',
			id: v.videoId || v.id
		}));

		console.log(`已恢复${selectedVideos.length}个选中视频到预览区域`);
	} else {
		console.log('没有找到匹配的视频，预览区域保持为空');
		previewVideoList.value = [];
	}

	// 更新全选状态
	isAllSelected.value = selectedCount.value === videoMaterials.value.length &&
		videoMaterials.value.length > 0 &&
		selectedCount.value > 0;
};

// 添加一个新的辅助函数，确保没有默认全选
const ensureNoDefaultSelection = () => {
	// 确保 isAllSelected 为 false
	isAllSelected.value = false;

	// 检查是否有视频被错误地全选
	const selectedCount = videoMaterials.value.filter(v => v.selected).length;

	// 如果所有视频都被选中，而且不是通过恢复状态选中的（即没有保存的选中ID）
	if (selectedCount === videoMaterials.value.length && videoMaterials.value.length > 0) {
		const savedIds = previewStore.selectedVideoIds || [];
		if (savedIds.length === 0) {
			console.log('检测到错误的全选状态，重置所有选中状态');

			// 重置所有视频为未选中
			videoMaterials.value.forEach(video => {
				video.selected = false;
			});

			// 清空预览区域
			previewVideoList.value = [];

			// 更新计数
			updateSelectedCount();
		}
	}
};

// 在组件挂载函数末尾调用
onMounted(() => {
	// 添加延时调用确保没有默认全选
	setTimeout(ensureNoDefaultSelection, 200);
});



/**
 * 处理提取内容项点击事件
 * 将视频播放设置到指定的时间点
 * @param {number} startTime - 内容项的开始时间（秒）
 */
const handleExtractedItemClick = (startTime) => {
	if (!videoPlayer.value) return;

	console.log('设置视频播放时间到:', startTime);

	// 保存当前视频状态以便恢复
	const wasPlaying = !videoPlayer.value.paused;

	// 设置视频当前时间到指定的开始时间
	videoPlayer.value.currentTime = startTime;

	// 如果视频原来在播放，则继续播放
	if (wasPlaying) {
		videoPlayer.value.play();
	}
};

// 添加handleCancelVideoSelection函数，仅调用cancelVideoSelection
const handleCancelVideoSelection = (removedVideo) => {
	console.log('处理视频选择取消事件', removedVideo);
	// 调用原有的取消选择函数
	cancelVideoSelection(removedVideo);
}

// 添加监听器：监控pinia中videoList的变化
watch(() => previewStore.videoList, (newVideoList) => {
	// 当pinia中videoList变为空数组时，取消所有视频的选中状态
	if (newVideoList.length === 0) {
		console.log('检测到pinia中videoList为空，取消所有视频选中状态');

		// 如果当前有选中的视频，才执行取消操作
		const hasSelectedVideos = videoMaterials.value.some(v => v.selected);

		if (hasSelectedVideos) {
			// 取消所有视频的选中状态
			videoMaterials.value.forEach(v => {
				v.selected = false;
			});

			// 清空预览区域
			previewVideoList.value = [];

			// 更新选中数量
			updateSelectedCount();

			console.log('已取消所有视频的选中状态');
		}
	}
}, { deep: true });

// 添加switchToPreviewTool方法，用于切换到预览工具
const switchToPreviewTool = async () => {
	// 如果已经在预览工具中，则不需要额外操作
	if (activeTool.value === 2) return;

	// 保存当前选中视频状态
	const currentSelectedVideos = videoMaterials.value.filter(v => v.selected);
	console.log('切换到预览前保存选中视频数量:', currentSelectedVideos.length);

	// 设置activeTool为2（预览工具）
	activeTool.value = 2;

	// 显示加载状态
	isLoadingPreview.value = true;

	try {
		// 获取预览视频数据，但不影响左侧选中状态
		await fetchPreviewVideos();

		// 确保选中状态不变
		if (currentSelectedVideos.length > 0) {
			console.log('切换工具后恢复选中状态');
			// 不需要重置所有视频选中状态
			// 只需确保之前选中的视频仍然被选中
			currentSelectedVideos.forEach(selectedVideo => {
				const matchingVideo = videoMaterials.value.find(v =>
					(v.id && v.id === selectedVideo.id) ||
					(v.videoId && v.videoId === selectedVideo.videoId)
				);
				if (matchingVideo) {
					matchingVideo.selected = true;
				}
			});

			// 更新Pinia存储的选中ID
			const selectedIds = currentSelectedVideos.map(v => v.id || v.videoId).filter(Boolean);
			if (selectedIds.length > 0) {
				previewStore.setSelectedVideoIds(selectedIds);
			}

			// 更新选中计数
			updateSelectedCount();
		}
	} catch (error) {
		console.error('获取预览视频失败:', error);
		// ElMessage.error('获取预览视频数据失败');
	} finally {
		// 无论成功或失败，最终将加载状态设为false
		isLoadingPreview.value = false;
	}
};

// 添加处理预览提取内容项点击的方法
const handlePreviewExtractedItemClick = (startTime) => {
	// 确保处于预览模式
	if (activeTool.value !== 2) {
		switchToPreviewTool();
	}

	// 延迟执行，确保切换到预览模式后再设置视频时间
	setTimeout(() => {
		handleExtractedItemClick(startTime);
	}, 300);
};

// 添加一个辅助函数，将"MM:SS"格式的时长转换为秒数
const durationToSeconds = (durationStr) => {
	// 处理null、undefined或空值的情况
	if (!durationStr) return 0;
	
	// 如果输入是数字类型，直接返回该数字（已经是秒数）
	if (typeof durationStr === 'number') return durationStr;
	
	// 如果是字符串但只包含数字（没有冒号），尝试解析为数字并返回
	if (typeof durationStr === 'string' && !durationStr.includes(':')) {
		const parsedNum = parseInt(durationStr, 10);
		if (!isNaN(parsedNum)) return parsedNum;
	}
	
	// 处理格式为"MM:SS"的时长字符串
	if (typeof durationStr === 'string') {
		const parts = durationStr.split(':');
		if (parts.length === 2) {
			// MM:SS 格式
			const minutes = parseInt(parts[0], 10) || 0;
			const seconds = parseInt(parts[1], 10) || 0;
			return minutes * 60 + seconds;
		} else if (parts.length === 3) {
			// HH:MM:SS 格式
			const hours = parseInt(parts[0], 10) || 0;
			const minutes = parseInt(parts[1], 10) || 0;
			const seconds = parseInt(parts[2], 10) || 0;
			return hours * 3600 + minutes * 60 + seconds;
		}
	}

	return 0;
};

// 计算消耗的算粒数量
const calculateComputationUnits = () => {
	// 初始化总时长（秒）
	let totalInputDurationInSeconds = 0;
	let outputDurationInSeconds = 0; // 使用文本长度计算的时长
	let totalVideoUnits = 0; // 新增：用于累加所有视频的算力

	// 计算输入时长 - 获取所有选中视频的总时长
	if (matchMode.value === 2) { // 视频模式
		const selectedVideos = videoMaterials.value.filter(video => video.selected);

		// 记录每个视频的处理情况，用于调试
		let videoTimingDetails = [];

		// 直接累加每个视频的时长，不考虑视频是否重复
		selectedVideos.forEach(video => {
			// 优先使用直接存储的durationInSeconds
			const seconds = video.durationInSeconds || durationToSeconds(video.duration || '0:00');
			totalInputDurationInSeconds += seconds;

			// 新增：单独计算每个视频的算力
			const videoMinutes = Math.ceil(seconds / 60); // 转换为分钟并向上取整
			const videoUnits = videoMinutes * 3; // 每分钟3算力
			totalVideoUnits += videoUnits; // 累加到总视频算力

			// 记录详情用于日志
			videoTimingDetails.push({
				title: video.title || '未命名视频',
				url: video.url,
				duration: video.duration || '0:00',
				durationInSeconds: seconds,
				videoMinutes: videoMinutes,
				videoUnits: videoUnits // 记录单个视频算力
			});
		});

		// 打印每个视频的时长详情
		console.log('选中视频时长详情:', videoTimingDetails);
		console.log(`视频模式: ${selectedVideos.length}个视频, 总时长: ${totalInputDurationInSeconds}秒, 总视频算力: ${totalVideoUnits}`);
	} else if (matchMode.value === 1 && selectedAlbumVideos.value) { // 专辑模式
		// 同样处理专辑模式
		let videoTimingDetails = [];

		selectedAlbumVideos.value.forEach(video => {
			// 优先使用直接存储的durationInSeconds
			const seconds = video.durationInSeconds || durationToSeconds(video.duration || '0:00');
			totalInputDurationInSeconds += seconds;

			// 新增：单独计算每个视频的算力
			const videoMinutes = Math.ceil(seconds / 60); // 转换为分钟并向上取整
			const videoUnits = videoMinutes * 3; // 每分钟3算力
			totalVideoUnits += videoUnits; // 累加到总视频算力

			// 记录详情用于日志
			videoTimingDetails.push({
				title: video.title || '专辑视频',
				url: video.url,
				duration: video.duration || '0:00',
				durationInSeconds: seconds,
				videoMinutes: videoMinutes,
				videoUnits: videoUnits // 记录单个视频算力
			});
		});

		console.log('专辑视频时长详情:', videoTimingDetails);
		console.log(`专辑模式: ${selectedAlbumVideos.value.length}个视频, 总时长: ${totalInputDurationInSeconds}秒, 总视频算力: ${totalVideoUnits}`);
	}

	// 使用文本长度计算配音时长 - 严格按照文本长度计算，不设最小值
	outputDurationInSeconds = calculateTextDuration();

	// 将计算的时长保存到store中，供其他地方使用
	if (previewStore) {
		previewStore.setDubbingDuration(outputDurationInSeconds);
		console.log("文本计算的配音时长已保存到store:", outputDurationInSeconds.toFixed(2), "秒");
	}

	console.log(`输入视频总时长: ${totalInputDurationInSeconds}秒, 文本计算的配音时长: ${outputDurationInSeconds.toFixed(2)}秒`);

	// 将文本计算的时长转换为分钟
	const outputMinutes = Math.ceil(outputDurationInSeconds / 60); // 向上取整
	const textUnits = outputMinutes * 3; // 文本的算力 = 分钟数 * 3

	console.log(`视频总算力: ${totalVideoUnits}算力, 文本算力: ${textUnits}算力`);

	// 最终算力 = 视频算力之和 + 文本算力
	const totalUnits = totalVideoUnits + textUnits;

	console.log(`最终总消耗算力: ${totalUnits}`);

	// 注意：这里移除了确保最小返回1个算粒的限制，严格按照计算结果返回
	return totalUnits;
};

// 添加处理扣费说明的函数
const handleFeeExplanation = () => {
	// 显示扣费说明弹窗
	feeExplanationDialogVisible.value = true;
}

// 添加一个函数来预加载视频并获取真实时长
const loadVideoAndGetDuration = (videoUrl, retryCount = 1) => {
	return new Promise((resolve) => {
		if (!videoUrl) {
			resolve({ durationInSeconds: 0, formattedDuration: '0:00' });
			return;
		}

		// 只在第一次尝试时输出一条简短的日志
		if (retryCount === 1) {
			console.log(`正在加载视频时长...`);
		}

		const video = document.createElement('video');
		video.preload = 'metadata';
		video.crossOrigin = 'anonymous';

		// 移除所有事件监听器的日志输出
		video.addEventListener('loadstart', () => { });
		video.addEventListener('canplay', () => { });


		// 添加事件监听器，在视频元数据加载完成后获取时长
		video.onloadedmetadata = () => {
			// 如果视频时长无效或为0，可能是元数据不完整
			if (!video.duration || video.duration === 0 || isNaN(video.duration)) {
				console.warn(`视频元数据加载，但时长无效，尝试等待更多数据...`);
				// 给予更多时间等待视频加载，以获取准确时长
				setTimeout(() => {
					processVideoData(video, videoUrl, resolve);
				}, 1000); // 额外等待1秒
			} else {
				processVideoData(video, videoUrl, resolve);
			}
		};



		// 设置视频源并开始加载
		video.src = videoUrl;
		// 使用muted属性可能有助于某些浏览器自动加载视频
		video.muted = true;
		// 对于某些浏览器，需要显式调用load方法
		video.load();
	});
};

// 辅助函数，处理视频数据
const processVideoData = (video, videoUrl, resolve) => {
	const durationInSeconds = Math.round(video.duration);

	// 将秒数转换为"MM:SS"格式
	const minutes = Math.floor(durationInSeconds / 60);
	const seconds = durationInSeconds % 60;
	const formattedDuration = `${minutes}:${seconds.toString().padStart(2, '0')}`;

	// 移除所有日志输出

	// 清理资源
	video.src = '';
	video.load();

	resolve({
		durationInSeconds,
		formattedDuration
	});
};
// 获取组件实例
const { proxy } = getCurrentInstance();

// 判断用户是否已登录
const checkUserLogin = () => {
	// 从本地存储获取user信息
	const userStorage = localStorage.getItem('user');
	if (!userStorage) return false;

	try {
		const userData = JSON.parse(userStorage);
		// 检查token是否为null或空
		return userData && userData.token && userData.token.trim() !== '';
	} catch (e) {
		console.error('解析用户数据失败:', e);
		return false;
	}
};

// 处理上传按钮点击事件
const handleUploadClick = () => {
	// 检查用户是否已登录
	if (!checkUserLogin()) {
		// 未登录，弹出登录弹窗
		proxy.$modal.open('组合式标题');
		return;
	}

	// 已登录，触发文件选择对话框
	proxy.$refs.fileInput.click();
}



// 添加监听器：监控pinia中videoList的变化
watch(() => previewStore.videoList, (newVideoList) => {
	// 当pinia中videoList变为空数组时，取消所有视频的选中状态
	if (newVideoList.length === 0) {
		console.log('检测到pinia中videoList为空，取消所有视频选中状态');

		// 如果当前有选中的视频，才执行取消操作
		const hasSelectedVideos = videoMaterials.value.some(v => v.selected);

		if (hasSelectedVideos) {
			// 取消所有视频的选中状态
			videoMaterials.value.forEach(v => {
				v.selected = false;
			});

			// 清空预览区域
			previewVideoList.value = [];

			// 更新选中数量
			updateSelectedCount();

			console.log('已取消所有视频的选中状态');
		}
	}
}, { deep: true });

// 监听previewVideoList变化，同步到previewStore
watch(() => previewVideoList.value, (newList) => {
	if (newList && newList.length > 0) {
		console.log('previewVideoList更新，同步到previewStore:', newList.length);

		// 将视频列表同步到previewStore
		previewStore.setVideoList(newList);

		// 同时确保视频URL也存在
		const firstVideo = newList[0];
		if (firstVideo && (firstVideo.url || firstVideo.videoUrl)) {
			const videoUrl = firstVideo.url || firstVideo.videoUrl;

			if (!previewStore.currentVideoUrl) {
				console.log('同步视频列表时发现Pinia中缺少URL，设置为:', videoUrl);
				previewStore.setCurrentVideoUrl(videoUrl);

				// 同时设置默认时间点
				if (!previewStore.videoTimestamps || previewStore.videoTimestamps.length === 0) {
					previewStore.setVideoTimestamps([]);
				}
			}
		}
	}
}, { deep: true });


// 添加一个函数计算文本长度对应的时长（秒）
const calculateTextDuration = () => {
	// 获取预览内容的文本
	const text = previewStore.content || '';

	// 获取文本长度
	const textLength = text.length;

	// 根据规则：310字符为1分钟，严格按照文本长度计算，不设最小值
	// 例如：7字符为0.0226分钟（约1.35秒）
	const minutes = textLength / 310; // 不向上取整，保留小数部分

	// 转换为秒
	const durationInSeconds = minutes * 60;

	console.log(`文本长度: ${textLength}字符，计算时长: ${minutes.toFixed(4)}分钟（${durationInSeconds.toFixed(2)}秒）`);

	// 返回时长（秒）
	return durationInSeconds;
};

// 添加一个带防抖的视频时长解析函数
const processVideoWithDelay = async (videos) => {
	// 使用局部的标志变量，而不是全局
	const processedUrls = new Set();

	// 视频分批处理，每次只处理一个视频，然后等待100ms
	for (let i = 0; i < videos.length; i++) {
		const video = videos[i];
		if (video.url && (!video.duration || video.duration === '00:00')) {
			// 检查URL是否已处理过，避免重复处理
			// if (processedUrls.has(video.url)) {
			// 	console.log(`跳过重复URL: ${video.title || '未命名'}`);
			// 	continue;
			// }

			try {
				console.log(`处理视频 ${i + 1}/${videos.length}: ${video.title || '未命名'}`);
				const durationInfo = await loadVideoAndGetDuration(video.url);
				video.duration = durationInfo.formattedDuration;
				video.durationInSeconds = durationInfo.durationInSeconds;

				// 将URL添加到已处理集合
				processedUrls.add(video.url);

				// 等待一段时间再处理下一个视频，避免频繁触发事件
				await new Promise(resolve => setTimeout(resolve, 100));
			} catch (error) {
				console.error(`视频处理失败: ${video.title || '未命名'}`);
			}
		}
	}

	return videos;
};

</script>

<style lang="scss" scoped>
.video-editing-container {
	display: grid;
	grid-template-rows: auto auto minmax(0, 1fr);
	/* 第一行Headbar，第二行OperationBar，第三行main-content占满剩余空间 */
	row-gap: 35px;
	/* 添加行间距 */
	min-height: 100vh;
	/* 从height: 100vh改为min-height: 100vh */
	height: auto;
	/* 添加自动高度 */
	background: #f7f7f9;
	width: 100%;
	overflow-x: hidden;
	/* 防止水平滚动 */

	:deep(.headbar-container) {
		grid-row: 1;
		/* Headbar在第一行 */
		width: 100%;
	}

	:deep(.operation-bar-container) {
		grid-row: 2;
		/* OperationBar在第二行 */
		width: 100%;
	}

	// 主要内容区域
	.main-content {
		grid-row: 3;
		/* 放在第三行 */
		display: flex;
		padding: 0 20px 20px 20px;
		margin-top: 0;
		/* 移除margin-top */
		background: #f7f7f9;

		// 左侧区域
		.left-section {
			display: flex;
			background: #fff;
			border-radius: 8px;
			margin-right: 20px;
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

			// 视频剪辑工具区域
			.video-editing-tools {
				flex: 0 0 505px;
				width: 505px;
				min-width: unset;
				background: #fff;
				border-radius: 0 8px 8px 0;
				padding: 20px;
				display: flex;
				flex-direction: column;
				height: 100%;
				z-index: 2;
				overflow-y: auto;

				// 工具栏样式
				.tools-wrapper {
					margin-bottom: 20px;
					position: relative;
					max-width: 100%;
					width: 100%;

					.tools-container {
						display: flex;
						align-items: center;
						justify-content: center;
						gap: 8px;
						background: transparent;
						border-radius: 4px;
						padding: 8px;
						position: relative;
						height: 52px;

						&::after {
							content: '';
							position: absolute;
							bottom: 0;
							left: 0;
							width: 100%;
							height: 1px;
							background-color: #0000001A;
						}
					}

					.tools-bar {
						display: flex;
						align-items: center;
						justify-content: space-between;
						width: 100%;
					}

					.tool-item {
						display: flex;
						align-items: center;
						justify-content: center;
						cursor: pointer;
						padding: 0;
						position: relative;
						flex: 1;
						height: 52px; // 已经是52px，不需要修改

						&::after {
							content: '';
							position: absolute;
							bottom: -2px; // 从-8px改为-2px
							left: 50%;
							width: 120px;
							height: 0;
							background-color: transparent;
							transition: all 0.3s;
							transform: translateX(-50%);
						}

						&.active {
							&::after {
								height: 4px;
								background: linear-gradient(90deg, #0AAF60, #A4CB55);
								opacity: 1;
								z-index: 1;
							}
						}

						&:hover {
							&::after {
								height: 4px;
								background: linear-gradient(90deg, #0AAF60, #A4CB55);
								opacity: 0.5;
								z-index: 1;
							}
						}

						.tool-icon {
							height: 52px; // 从36px改为52px
							width: 120px; // 从auto改为120px
							object-fit: contain;
						}
					}
				}

				// 视频内容区域
				.video-content {
					flex: 1;
					display: flex;
					flex-direction: column;
					overflow-y: auto;
				}

				h3 {
					font-size: 16px;
					font-weight: 500;
					color: #303133;
					margin-bottom: 15px;
				}

				// 匹配内容样式
				.match-content {
					// margin-top: 10px;
					// padding: 10px 0;

					.match-buttons {
						display: flex;
						margin-bottom: 20px;
						border-radius: 4px;
						overflow: hidden;
						width: fit-content;
						border: 1px solid #0AAF60;
					}

					.btn-tab {
						border: none;
						padding: 8px 20px;
						font-size: 14px;
						font-weight: 500;
						cursor: pointer;
						transition: all 0.3s;
						background: #fff;
						color: #0AAF60;

						&:hover:not(.active) {
							background: #f5f5f5;
						}

						&.active {
							background: #0AAF60;
							color: white;
						}
					}

					.material-grid {
						display: flex;
						flex-direction: column;
						gap: 15px;
						padding: 5px;

						.filter-bar {
							display: flex;
							align-items: center;
							// margin-bottom: 20px;
							flex-wrap: nowrap;

							.filter-item {
								display: flex;
								align-items: center;
								margin-right: 20px;

								.filter-label {
									font-size: 14px;
									color: #606266;
									margin-right: 8px;
								}
							}
						}

						.albums-container {
							display: grid;
							grid-template-columns: repeat(5, 1fr);
							gap: 15px;

							.material-item {
								aspect-ratio: 1/1;
								background-color: #f5f5f5;
								border-radius: 6px;
								display: flex;
								align-items: center;
								justify-content: center;
								cursor: pointer;
								transition: all 0.3s;
								position: relative;

								&:hover {
									transform: translateY(-3px);
									box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
								}

								.material-placeholder {
									color: #909399;
									font-size: 14px;
									text-align: center;
									user-select: none;
								}
							}
						}
					}

					// 指定风格模式样式
					.style-content {
						.filter-bar {
							display: flex;
							align-items: center;
							margin-bottom: 20px;
							flex-wrap: nowrap;

							.filter-item {
								display: flex;
								align-items: center;
								margin-right: 20px;

								.filter-label {
									font-size: 14px;
									color: #606266;
									margin-right: 8px;
								}

								.time-select,
								.source-select {
									padding: 0 15px;
									height: 32px;
									border: 1px solid #DCDFE6;
									border-radius: 4px;
									display: flex;
									align-items: center;
									cursor: pointer;
									font-size: 14px;
									color: #303133;

									span {
										margin-right: 8px;
									}

									.arrow-down {
										border-style: solid;
										border-width: 5px 5px 0 5px;
										border-color: #C0C4CC transparent transparent transparent;
										display: inline-block;
										width: 0;
										height: 0;
									}
								}
							}

							.selection-count {
								margin-left: auto;
								display: flex;
								align-items: center;
								height: 32px;

								.select-all {
									display: flex;
									align-items: center;
									cursor: pointer;
									margin-right: 15px;

									input[type="checkbox"] {
										margin-right: 5px;
										position: relative;
										width: 16px;
										height: 16px;
										appearance: none;
										border: 1px solid #dcdfe6;
										border-radius: 2px;
										background-color: #fff;
										cursor: pointer;
										transition: border-color .25s, background-color .25s;

										&:checked {
											background-color: #0AAF60;
											border-color: #0AAF60;

											&::after {
												content: '';
												position: absolute;
												top: 3px;
												left: 5px;
												width: 3px;
												height: 7px;
												border: solid #fff;
												border-width: 0 1px 1px 0;
												transform: rotate(45deg);
											}
										}

										&:hover {
											border-color: #c0c4cc;
										}
									}

									span {
										font-size: 14px;
										color: #606266;
									}
								}

								.count {
									font-size: 14px;
									color: #909399;
								}
							}
						}

						.video-grid {
							flex: 1;
							display: grid;
							grid-template-columns: repeat(auto-fill, 200px);
							gap: 20px;
							justify-content: space-between;
							max-height: calc(100vh - 500px); // 设置最大高度，留出顶部空间、
							overflow-y: auto; // 启用垂直滚动
							padding-right: 10px; // 为滚动条预留空间
							overflow-y: auto;
							padding-right: 10px;
							margin-bottom: 20px; // 添加底部边距，为按钮留出空间

							@media screen and (max-width: 1500px) {
								max-height: calc(100vh - 252px);
							}

							@media screen and (max-width: 1480px) {
								max-height: calc(100vh - 251px);
							}

							@media screen and (max-width: 1366px) {
								max-height: calc(100vh - 200px);
							}

							@media screen and (max-width: 1024px) {
								max-height: calc(100vh - 300px);
							}

							& {
								// 隐藏滚动条但保留滚动功能
								scrollbar-width: none; // Firefox
								-ms-overflow-style: none; // IE and Edge

								// 优化滚动体验
								scroll-behavior: smooth;
								-webkit-overflow-scrolling: touch;
							}

							&::-webkit-scrollbar {
								display: none; // Chrome, Safari, Opera
							}

							// 以下是嵌套选择器，不需要修改
							.video-upload-item {
								width: 200px;
								height: 108px;
								background-color: #F5F7FA;
								border-radius: 4px;
								display: flex;
								flex-direction: column;
								align-items: center;
								justify-content: center;
								cursor: pointer;
								border: 1px dashed #dcdfe6;
								position: relative;
								overflow: hidden;

								.upload-icon {
									width: 40px;
									height: 40px;
									background-color: #E4E7ED;
									border-radius: 50%;
									display: flex;
									align-items: center;
									justify-content: center;
									margin-bottom: 10px;

									.plus-icon {
										font-size: 24px;
										color: #909399;
										font-weight: 300;
									}
								}

								.upload-text {
									font-size: 14px;
									color: #909399;
								}

								.upload-progress {
									width: 100%;
									height: 100%;
									display: flex;
									flex-direction: column;
									justify-content: center;
									padding: 0 16px;

									.progress-info {
										display: flex;
										justify-content: space-between;
										margin-bottom: 8px;

										.file-name {
											font-size: 12px;
											color: #606266;
											white-space: nowrap;
											overflow: hidden;
											text-overflow: ellipsis;
											max-width: 70%;
										}

										.progress-percent {
											font-size: 12px;
											color: #0AAF60;
											font-weight: 500;
										}
									}

									.progress-bar-bg {
										width: 100%;
										height: 6px;
										background-color: #E4E7ED;
										border-radius: 3px;
										overflow: hidden;
										margin-bottom: 12px;

										.progress-bar {
											height: 100%;
											background-color: #0AAF60;
											border-radius: 3px;
											transition: width 0.3s;
										}
									}

									.cancel-upload {
										font-size: 12px;
										color: #909399;
										text-align: center;
										cursor: pointer;

										&:hover {
											color: #F56C6C;
										}
									}
								}
							}

							.video-item {
								cursor: pointer;
								width: 200px;

								.video-thumbnail {
									position: relative;
									width: 200px;
									height: 108px;
									background-color: #333; // 默认背景色
									border-radius: 4px;
									overflow: hidden;
									margin-bottom: 8px;
									// 移除伪元素背景

									// 移除时长显示元素
									// .video-duration {
									//     position: absolute;
									//     left: 10px;
									//     bottom: 10px;
									//     padding: 2px 6px;
									//     background-color: rgba(0, 0, 0, 0.6);
									//     color: #fff;
									//     font-size: 12px;
									//     border-radius: 2px;
									//     z-index: 2; // 确保时长显示在缩略图上方
									// }

									.select-indicator {
										position: absolute;
										top: 4px;
										right: 10px;
										width: 24px;
										height: 24px;
										border-radius: 50%;
										background-color: transparent;
										/* 移除背景色 */
										display: flex;
										align-items: center;
										justify-content: center;
										z-index: 2;
										transition: all 0.2s ease;
										/* 添加过渡动画 */

										/* 添加图片图标的样式 */
										.select-icon {
											width: 100%;
											height: 100%;
											object-fit: contain;
											transition: all 0.2s ease;
										}

										&.active {
											background-color: transparent;
											/* 移除背景色 */
											border: none;
											/* 移除边框 */

											.select-icon {
												transform: scale(1.1);
												/* 轻微放大图标 */
											}
										}
									}
								}

								.video-title {
									font-size: 14px;
									color: #303133;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
									width: 200px;
								}
							}
						}
					}
				}
			}
		}

		// 右侧预览区域
		.right-preview {
			flex: 1;
			background: #FFFFFF;
			border-radius: 8px;
			padding: 20px;
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
		}
	}
}

// 预览工具内容样式
.preview-content-tool {
	flex: 1;
	display: flex;
	flex-direction: column;
	height: 100%;
	// max-height: calc(100vh - 160px);
	/* 添加最大高度限制，减去顶部元素的高度 */
	overflow: hidden;
	/* 防止内容溢出 */

	// 视频缩略图列表容器
	.video-thumbnails-list-container {
		position: relative;
		margin-bottom: 15px;
	}

	// 视频缩略图列表
	.video-thumbnails-list {
		display: flex;
		overflow-x: auto;
		padding: 0 30px 0 0; // 添加右侧padding，为固定的滑动按钮留出空间
		position: relative;

		/* 隐藏滚动条但保留功能 */
		&::-webkit-scrollbar {
			height: 0;
			display: none;
		}

		.thumbnail-item {
			flex: 0 0 auto;
			width: 86px;
			margin-right: 10px;
			cursor: pointer;
			transition: all 0.3s;
			border-radius: 4px;
			overflow: hidden;
			position: relative;

			/* 添加悬停时隐藏阴影层的效果 */
			&:hover .thumbnail-overlay,
			&.active .thumbnail-overlay {
				opacity: 0;
				visibility: hidden;
			}
		}

		.thumbnail-item.active {
			/* 移除边框特效 */
		}

		.thumbnail-image {
			width: 100%;
			height: 48px;
			background-color: #333;
			border-radius: 4px;
			position: relative;
			overflow: hidden;
		}

		/* 保留阴影层样式并添加过渡效果 */
		.thumbnail-overlay {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: rgba(0, 0, 0, 0.5);
			display: flex;
			align-items: center;
			justify-content: center;
			transition: opacity 0.3s, visibility 0.3s;
		}

		.thumbnail-title {
			color: #fff;
			font-size: 12px;
			text-align: center;
			padding: 0 5px;
			width: 100%;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}

	// 左侧滑动按钮样式
	.thumbnail-scroll-left {
		position: absolute;
		left: 0;
		top: 0;
		height: 45px;
		width: 30px;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		border-radius: 4px 0 0 4px;
		color: #fff;
		font-size: 24px;
		z-index: 2;

		&:hover {
			background: rgba(0, 0, 0, 0.7);
		}
	}

	// 右侧滑动按钮样式 - 固定在右侧
	.thumbnail-scroll-right {
		position: absolute;
		right: 0;
		top: 0;
		height: 45px;
		width: 30px;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		border-radius: 0 4px 4px 0;
		color: #fff;
		font-size: 24px;
		z-index: 3; // 增加z-index确保在内容之上

		&:hover {
			background: rgba(0, 0, 0, 0.7);
		}
	}

	// 主视频播放区域
	.main-video-player {
		flex: 1;
		margin-bottom: 20px;
		min-height: 400px; // 从300px增加到400px，确保更大的初始高度
		max-height: calc(100vh - 260px); // 从360px减少到260px，留出更多空间给视频
		/* 添加最大高度限制，减去其他元素的高度 */
		overflow-y: auto;
		/* 添加垂直滚动条 */

		.video-container {
			width: 100%;
			height: 100%;
			border-radius: 8px;
			overflow: hidden;
			background: #000;
			position: relative;

			.video-element {
				width: 100%;
				height: 100%;
				object-fit: contain; // 保持contain，确保视频完整显示不失真
				min-height: 400px; // 添加最小高度约束，确保视频元素本身也足够大
			}
		}
	}

	// 操作按钮
	.video-action-buttons {
		display: flex;
		justify-content: space-between;
		padding: 10px 0;

		.left-buttons {
			display: flex;
			gap: 15px;
		}

		.right-buttons {
			display: flex;
		}

		.btn-action {
			display: flex;
			align-items: center;
			padding: 8px 15px;
			border: none;
			border-radius: 4px;
			font-size: 14px;
			cursor: pointer;
			transition: all 0.3s;

			.btn-icon {
				width: 16px;
				height: 16px;
				margin-right: 5px;

				&.download-icon {
					filter: brightness(0) saturate(100%) invert(57%) sepia(86%) saturate(388%) hue-rotate(108deg) brightness(94%) contrast(98%);
				}
			}

			&.btn-white {
				background: #FFFFFF;
				color: #606266;
				border: 1px solid #DCDFE6;

				&:hover {
					background: #F5F7FA;
				}
			}

			&.btn-outline-green {
				border-color: #0AAF60;

				&:hover {
					border-color: #09954F;
				}
			}

			&.btn-green {
				background: #0AAF60;
				color: white;

				&:hover {
					background: #09954F;
				}
			}
		}
	}

}

// 匹配设置工具内容样式
.match-settings-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	height: 100%;

	.settings-title {
		font-size: 16px;
		font-weight: 500;
		color: #303133;
		margin-bottom: 20px;
		padding: 0 20px;
	}

	.settings-form {
		flex: 1;
		padding: 0 20px 20px;
		overflow-y: auto;

		// 自定义滑块样式
		.slider-wrapper {
			flex: 1;
			width: 100%;
			/* 改为100%，使用父容器宽度 */
			max-width: 300px;
			/* 添加最大宽度限制 */
			padding: 0 8px;
			// background-color: #f7f8fa;
			border-radius: 2px;
			// border: 1px solid rgba(220, 223, 230, 0.5);
			display: flex;
			align-items: center;
		}

		:deep(.custom-slider) {
			width: 100%;
			/* 改为100%，自适应父容器宽度 */
			margin: 16px 0;

			.el-slider__runway {
				height: 6px;
				background-color: #E4E7ED;
				border-radius: 3px;
			}

			.el-slider__bar {
				height: 6px;
				background-color: #0AAF60;
				border-radius: 3px;
			}

			.el-slider__button-wrapper {
				top: -9px;
				height: 24px;
				width: 24px;
				z-index: 2;
				cursor: pointer;
			}

			.el-slider__button {
				border: 2px solid #0AAF60;
				width: 16px;
				height: 16px;
				background-color: #fff;
			}

			// 调整停止点样式
			.el-slider__stop {
				height: 6px;
				width: 1px;
				position: absolute;
				background-color: #fff;
				top: 0;
			}
		}

		.el-form-item {
			margin-bottom: 24px;

			.input-item {
				display: flex;
				align-items: flex-start;
				margin-bottom: 5px;

				.input-label {
					font-size: 14px;
					font-weight: 500;
					color: #303133;
					width: 140px;
					flex-shrink: 0;
					line-height: 32px;
				}

				.input-control-wrap {
					flex: 1;
					display: flex;
					align-items: center;
					flex-direction: column;
					justify-content: center;
					padding-top: 4px;

					.input-tip {
						font-size: 14px;
						color: #909399;
						line-height: 24px;
					}
				}

				.el-radio-group,
				.el-slider,
				.slider-wrapper {
					flex: 1;
					width: 383px;
				}
			}

			.el-form-item__label {
				font-size: 14px;
				font-weight: 500;
				color: #303133;
				padding-bottom: 8px;
			}

			.count-input {
				width: 90px !important;
				height: 24px !important;

				:deep(.el-input__inner) {
					height: 24px !important;
					line-height: 24px !important;
					padding: 0 8px;
					font-size: 14px;
				}

				:deep(.el-input__wrapper) {
					padding: 0;
					border: 1px solid #0AAF60;
					box-shadow: none !important;
					border-radius: 2px;
					height: 24px;
				}

				:deep(.el-input-number__decrease),
				:deep(.el-input-number__increase) {
					border: none;
					height: 11px !important;
					line-height: 11px !important;
					background-color: #f5f7fa;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				:deep(.el-input-number__increase) {
					border-bottom: 1px solid #dcdfe6;
				}

				:deep(.el-input-number__controls) {
					right: 1px;
				}
			}

			.size-inputs-container {
				flex: 1;
				display: flex;
				flex-direction: column;
			}

			.size-inputs {
				display: flex;
				align-items: center;
				margin-bottom: 4px;

				.size-input {
					width: 64px;

					:deep(.el-input__wrapper) {
						height: 24px;
						padding: 0 8px;
						box-shadow: 0 0 0 1px #DCDFE6 inset;
						border-radius: 2px;
					}

					:deep(.el-input__inner) {
						height: 24px;
						line-height: 24px;
						font-size: 14px;
						padding: 0;
					}
				}

				.size-separator {
					margin: 0 10px;
					color: #606266;
				}
			}

			.switch-wrapper {
				display: flex;
				align-items: center;
				margin-bottom: 4px;

				// 自定义开关样式
				:deep(.custom-switch) {
					.el-switch__core {
						background-color: #DCDFE6;
						border-color: #DCDFE6;
						width: 52px !important;
						height: 24px;
						border-radius: 12px;
					}

					.el-switch__action {
						height: 20px;
						width: 20px;
						margin: 2px;
					}

					&.is-checked {
						.el-switch__core {
							background-color: #0AAF60 !important;
							border-color: #0AAF60 !important;

							&::before {
								content: "ON";
								position: absolute;
								left: 7px;
								top: 0;
								height: 100%;
								display: flex;
								align-items: center;
								justify-content: flex-start;
								color: #FFFFFF;
								font-size: 12px;
								font-weight: bold;
							}
						}

						.el-switch__action {
							left: calc(100% - 22px);
						}
					}
				}
			}

			.size-tip {
				font-size: 12px;
				color: #909399;
				line-height: 18px;
			}

			.horizontal-slider-item {
				align-items: center !important;
				display: flex;
				/* 确保使用flex布局 */
				flex-wrap: wrap;
				/* 在小屏幕上允许换行 */

				.input-label {
					line-height: normal;
					margin-right: 10px;
					width: 120px !important;
					/* 减小标签宽度 */
					flex-shrink: 0;
					/* 防止标签宽度被压缩 */
				}

				.slider-wrapper {
					padding-top: 0 !important;
					display: flex;
					align-items: center;
					flex: 1;
					/* 占用剩余空间 */
					min-width: 200px;
					/* 设置最小宽度 */
					margin-top: 0 !important;
					margin-bottom: 0 !important;

					.custom-slider {
						margin: 0 !important;
					}
				}
			}
		}

		.form-buttons {
			display: flex;
			justify-content: center;
			gap: 20px;
			margin-top: 30px;

			.el-button {
				min-width: 100px;

				&.el-button--success {
					background-color: #0AAF60;
					border-color: #0AAF60;

					&:hover,
					&:focus {
						background-color: #09954F;
						border-color: #09954F;
					}
				}
			}
		}
	}
}

// 提示文本样式
.effect-tip {
	font-size: 12px !important;
	color: #909399 !important;
	line-height: 16px !important;
	margin-top: 8px !important;
	margin-left: 0 !important;
}

// 添加按钮容器样式
.match-button-container {
	display: flex;
	justify-content: center;
	padding: 30px 0;
	width: 100%;
	background: #fff;
	border-top: 1px solid #EBEEF5;
	margin-top: auto; // 使用 margin-top: auto 将按钮推到底部
}

// 添加按钮样式
.smart-match-button {
	width: 100%;
	height: 42px;
	background: linear-gradient(90deg, #0AAF60 0%, #A4CB55 100%);
	border: none;
	border-radius: 4px;
	color: white;
	font-size: 16px;
	font-weight: 500;
	cursor: pointer;
	transition: opacity 0.3s;

	&:hover {
		opacity: 0.9;
	}

	&:active {
		opacity: 0.8;
	}
}

/* 添加加载效果的样式 */
.preview-loading-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(255, 255, 255, 0.8);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	z-index: 10;
}

.loading-spinner {
	width: 50px;
	height: 50px;
	border: 4px solid #f3f3f3;
	border-top: 4px solid #0AAF60;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 15px;
}

.loading-text {
	font-size: 16px;
	color: #606266;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

/* 添加选中专辑项的样式 */
.albums-container {
	.material-item {
		// 现有样式...
		transition: all 0.3s ease; // 添加过渡效果

		&.selected {
			border: 2px solid #0AAF60; // 添加绿色边框
			box-shadow: 0 0 6px rgba(10, 175, 96, 0.3); // 添加阴影效果
		}
	}
}

// 确保缩略图样式正确
.loading-videos {
	margin-top: 15px;

	.loading-videos-title {
		font-size: 14px;
		font-weight: 500;
		margin-bottom: 10px;
	}

	.video-item {
		display: flex;
		margin-bottom: 10px;

		.video-thumbnail {
			width: 120px;
			height: 68px;
			border-radius: 4px;
			background-color: #f0f0f0;
			overflow: hidden;
			flex-shrink: 0;

			.thumbnail-placeholder {
				width: 100%;
				height: 100%;
				background-color: #e0e0e0;
				display: flex;
				align-items: center;
				justify-content: center;

				&:after {
					content: '无缩略图';
					font-size: 12px;
					color: #909399;
				}
			}
		}

		.video-info {
			margin-left: 10px;
			display: flex;
			flex-direction: column;
			justify-content: center;

			.video-title {
				font-size: 14px;
				font-weight: 500;
				color: #303133;
				margin-bottom: 4px;
				max-width: 200px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}
}

/* 提取内容调试区域样式 */
.extracted-content-debug {
	margin-top: 20px;
	padding: 15px;
	background-color: #f5f7fa;
	border-radius: 8px;

	h3 {
		font-size: 16px;
		margin-bottom: 10px;
		color: #606266;
	}

	.content-list {
		max-height: 200px;
		overflow-y: auto;

		.content-item {
			padding: 8px;
			border-bottom: 1px solid #ebeef5;

			&:last-child {
				border-bottom: none;
			}

			.content-text {
				font-size: 14px;
				margin-bottom: 5px;
				white-space: nowrap !important;
				/* 添加不折行样式 */
				overflow: hidden !important;
				text-overflow: ellipsis !important;
			}

			.content-time {
				font-size: 12px;
				color: #909399;
				display: flex;
				gap: 10px;
			}
		}
	}
}

// 提取工具区域样式
.extraction-tools-section {
	margin-bottom: 20px;
	padding: 15px;
	background: #f9f9f9;
	border-radius: 8px;
	border: 1px solid #eee;

	.section-title {
		font-size: 16px;
		font-weight: 500;
		color: #303133;
		margin-bottom: 15px;
	}
}

// 文案提取结果区域样式
.extracted-text-area {
	margin-top: 20px;
	background: #fff;
	border-radius: 8px;
	padding: 15px;
	border: 1px solid #eaeaea;

	.extracted-text-title {
		font-size: 15px;
		font-weight: 500;
		color: #303133;
		margin-bottom: 10px;
	}

	.extracted-text-content {
		max-height: 200px;
		overflow-y: auto;
		padding: 10px;
		background: #f5f7fa;
		border-radius: 4px;
		font-size: 14px;
		line-height: 1.6;
		color: #606266;
		white-space: pre-wrap;
		margin-bottom: 15px;
	}

	.extracted-text-actions {
		display: flex;
		justify-content: flex-end;
		gap: 10px;

		.action-btn {
			padding: 8px 15px;
			border-radius: 4px;
			border: none;
			font-size: 14px;
			display: flex;
			align-items: center;
			gap: 5px;
			cursor: pointer;
			transition: all 0.3s;

			img {
				width: 16px;
				height: 16px;
				object-fit: contain;
			}

			&.copy-btn {
				background: #fff;
				color: #606266;
				border: 1px solid #dcdfe6;

				&:hover {
					border-color: #c0c4cc;
					background: #f5f7fa;
				}
			}

			&.use-btn {
				background: #0AAF60;
				color: #fff;

				&:hover {
					background: #09954F;
				}
			}
		}
	}
}

// 添加智能匹配按钮，与指定视频页面样式一致
.album-match-button-container {
	display: flex;
	justify-content: center;
	padding: 30px 0;
	width: 100%;
	background: #fff;
	border-top: 1px solid #EBEEF5;
	margin-top: auto; // 使用 margin-top: auto 将按钮推到底部
}

/* 全局确保提取内容文本不折行 */
:deep(.content-text),
:deep(.extracted-content-item .content-text),
:deep(.preview-section .content-text) {
	white-space: nowrap !important;
	overflow: hidden !important;
	text-overflow: ellipsis !important;
	display: block !important;
	width: 100% !important;
}

/* 为日期选择器弹窗添加在小屏幕上的自适应样式 */
.date-picker-small {
	&.el-picker-panel.el-date-range-picker {
		@media (max-width: 768px) {
			width: auto !important;
			min-width: 320px !important;

			.el-date-range-picker__content {
				margin: 0 10px !important;
			}

			.el-date-range-picker__header {
				margin-bottom: 8px !important;
				padding: 8px 5px !important;
			}

			.el-picker-panel__content {
				margin: 0 !important;
				padding: 0 8px !important;
			}

			.el-date-table th {
				padding: 2px !important;
				font-size: 12px !important;
			}

			.el-date-table td {
				padding: 0 2px !important;
				height: 24px !important;
				width: 24px !important;

				.el-date-table__adjacent-month {
					color: #c0c4cc !important;
				}

				.el-date-table__adjacent-month .cell {
					color: #c0c4cc !important;
				}

				.cell {
					height: 24px !important;
					width: 24px !important;
					line-height: 24px !important;
					font-size: 10px !important;
				}
			}
		}
	}
}
</style>
